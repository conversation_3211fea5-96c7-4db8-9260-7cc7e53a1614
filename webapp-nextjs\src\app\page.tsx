'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import Link from 'next/link'
import {
  Cable,
  Activity,
  BarChart3,
  Building2,
  ClipboardList,
  TrendingUp,
  Users,
  Clock,
  CheckCircle
} from 'lucide-react'

export default function Home() {
  const { user, cantiere, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/login')
      } else if (user?.ruolo === 'owner') {
        router.push('/admin')
      } else if (user?.ruolo === 'user') {
        router.push('/cantieri')
      } else if (user?.ruolo === 'cantieri_user') {
        router.push('/cavi')
      } else if (cantiere) {
        router.push('/cavi')
      }
    }
  }, [isAuthenticated, isLoading, user, cantiere, router])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center mx-auto mb-4">
            <Cable className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-slate-900 mb-2">CABLYS</h1>
          <p className="text-slate-600">Caricamento in corso...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Il middleware gestirà il redirect
  }

  // Dati mock per la dashboard
  const dashboardData = {
    totalCantieri: 5,
    activeCantieri: 3,
    totalCables: 2450,
    installedCables: 1680,
    completionPercentage: 68.6,
    activeTeams: 12,
    todayInstallations: 45,
    weeklyTarget: 300
  }

  const quickActions = [
    { 
      title: 'Gestione Cavi', 
      description: 'Visualizza e gestisci i cavi del cantiere',
      href: '/cavi',
      icon: Cable,
      color: 'bg-blue-500'
    },
    { 
      title: 'Nuova Comanda', 
      description: 'Crea una nuova comanda di lavoro',
      href: '/comande/new',
      icon: ClipboardList,
      color: 'bg-green-500'
    },
    { 
      title: 'Produttività', 
      description: 'Monitora le performance del team',
      href: '/productivity',
      icon: Activity,
      color: 'bg-purple-500'
    },
    { 
      title: 'Report', 
      description: 'Genera report di avanzamento',
      href: '/reports',
      icon: BarChart3,
      color: 'bg-orange-500'
    }
  ]

  const recentActivities = [
    { time: '10:30', action: 'Cavo installato', details: 'FG16OM4-24 - Settore A', status: 'success' },
    { time: '10:15', action: 'Comanda completata', details: 'CMD-2024-001', status: 'success' },
    { time: '09:45', action: 'Nuovo cantiere', details: 'Cantiere Milano Nord', status: 'info' },
    { time: '09:30', action: 'Alert qualità', details: 'Controllo necessario Settore B', status: 'warning' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <Building2 className="h-8 w-8 text-blue-600" />
              Dashboard CABLYS
            </h1>
            <p className="text-slate-600 mt-1">
              Benvenuto, {user ? user.username : cantiere?.commessa}
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              Sistema Operativo
            </Badge>
            <div className="text-right text-sm text-slate-600">
              <p>Ultimo aggiornamento</p>
              <p className="font-medium">Ora: {new Date().toLocaleTimeString('it-IT')}</p>
            </div>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Avanzamento Totale</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{dashboardData.completionPercentage}%</div>
              <Progress value={dashboardData.completionPercentage} className="mt-2" />
              <p className="text-xs text-slate-500 mt-2">
                {dashboardData.installedCables} di {dashboardData.totalCables} cavi
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Cantieri Attivi</CardTitle>
              <Building2 className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{dashboardData.activeCantieri}</div>
              <p className="text-xs text-slate-500">di {dashboardData.totalCantieri} totali</p>
              <div className="flex items-center mt-2">
                <CheckCircle className="h-3 w-3 text-green-500 mr-1" />
                <span className="text-xs text-green-600">Tutti operativi</span>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Team Attivi</CardTitle>
              <Users className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{dashboardData.activeTeams}</div>
              <p className="text-xs text-slate-500">squadre operative</p>
              <div className="flex items-center mt-2">
                <Activity className="h-3 w-3 text-purple-500 mr-1" />
                <span className="text-xs text-purple-600">In servizio</span>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Installazioni Oggi</CardTitle>
              <Clock className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{dashboardData.todayInstallations}</div>
              <p className="text-xs text-slate-500">Target: {dashboardData.weeklyTarget}/settimana</p>
              <Progress value={(dashboardData.todayInstallations / dashboardData.weeklyTarget) * 100} className="mt-2" />
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions e Recent Activities */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-600" />
                Azioni Rapide
              </CardTitle>
              <CardDescription>Accesso diretto alle funzioni principali</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {quickActions.map((action, index) => {
                  const Icon = action.icon
                  return (
                    <Link key={index} href={action.href}>
                      <Card className="hover:shadow-md transition-shadow cursor-pointer border-2 hover:border-blue-200">
                        <CardContent className="p-4">
                          <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center mb-3`}>
                            <Icon className="h-5 w-5 text-white" />
                          </div>
                          <h4 className="font-medium text-slate-900 mb-1">{action.title}</h4>
                          <p className="text-xs text-slate-600">{action.description}</p>
                        </CardContent>
                      </Card>
                    </Link>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activities */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-green-600" />
                Attività Recenti
              </CardTitle>
              <CardDescription>Ultime operazioni del sistema</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-slate-50 rounded-lg">
                    <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                      activity.status === 'success' ? 'bg-green-500' :
                      activity.status === 'warning' ? 'bg-orange-500' :
                      'bg-blue-500'
                    }`}></div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-slate-900">{activity.action}</p>
                        <span className="text-xs text-slate-500">{activity.time}</span>
                      </div>
                      <p className="text-sm text-slate-600 truncate">{activity.details}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

      </div>
    </div>
  )
}
