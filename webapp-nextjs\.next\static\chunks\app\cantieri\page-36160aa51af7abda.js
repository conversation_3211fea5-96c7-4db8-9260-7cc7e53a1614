(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[222],{23227:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});let i=(0,s(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},40321:(e,a,s)=>{Promise.resolve().then(s.bind(s,75355))},54165:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>h,L3:()=>u,c7:()=>x,lG:()=>c,rr:()=>g,zM:()=>l});var i=s(95155);s(12115);var t=s(50237),n=s(54416),r=s(59434);function c(e){let{...a}=e;return(0,i.jsx)(t.bL,{"data-slot":"dialog",...a})}function l(e){let{...a}=e;return(0,i.jsx)(t.l9,{"data-slot":"dialog-trigger",...a})}function o(e){let{...a}=e;return(0,i.jsx)(t.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...s}=e;return(0,i.jsx)(t.hJ,{"data-slot":"dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s})}function m(e){let{className:a,children:s,showCloseButton:c=!0,...l}=e;return(0,i.jsxs)(o,{"data-slot":"dialog-portal",children:[(0,i.jsx)(d,{}),(0,i.jsxs)(t.UC,{"data-slot":"dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...l,children:[s,c&&(0,i.jsxs)(t.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,i.jsx)(n.A,{}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...s}=e;return(0,i.jsx)("div",{"data-slot":"dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",a),...s})}function h(e){let{className:a,...s}=e;return(0,i.jsx)("div",{"data-slot":"dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...s})}function u(e){let{className:a,...s}=e;return(0,i.jsx)(t.hE,{"data-slot":"dialog-title",className:(0,r.cn)("text-lg leading-none font-semibold",a),...s})}function g(e){let{className:a,...s}=e;return(0,i.jsx)(t.VY,{"data-slot":"dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...s})}},75355:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>z});var i=s(95155),t=s(12115),n=s(35695),r=s(66695),c=s(30285),l=s(62523),o=s(85057),d=s(85127),m=s(54165),x=s(40283),h=s(25731),u=s(51154),g=s(84616),j=s(85339),p=s(47924),v=s(23227);let f=(0,s(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var N=s(92657),_=s(13717);function z(){let{user:e,isAuthenticated:a,isLoading:s}=(0,x.A)(),z=(0,n.useRouter)(),[C,w]=(0,t.useState)([]),[y,b]=(0,t.useState)(!0),[k,A]=(0,t.useState)(""),[E,M]=(0,t.useState)(""),[S,J]=(0,t.useState)(!1),[L,F]=(0,t.useState)(!1),[I,$]=(0,t.useState)(null),[D,G]=(0,t.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""});(0,t.useEffect)(()=>{s||a||z.push("/login")},[a,s,z]),(0,t.useEffect)(()=>{a&&O()},[a]);let O=async()=>{try{b(!0);let e=await h._I.getCantieri();w(e)}catch(e){console.error("Errore nel caricamento cantieri:",e),A("Errore nel caricamento dei cantieri")}finally{b(!1)}},Z=async()=>{try{await h._I.createCantiere(D),J(!1),G({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),O()}catch(e){console.error("Errore nella creazione cantiere:",e),A("Errore nella creazione del cantiere")}},H=async()=>{if(I)try{await h._I.updateCantiere(I.id_cantiere,D),F(!1),$(null),O()}catch(e){console.error("Errore nella modifica cantiere:",e),A("Errore nella modifica del cantiere")}},P=e=>{localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",e.commessa),z.push("/cantieri/".concat(e.id_cantiere))},V=e=>{$(e),G({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),F(!0)},q=e=>{navigator.clipboard.writeText(e)},B=C.filter(e=>{var a,s;return e.commessa.toLowerCase().includes(E.toLowerCase())||(null==(a=e.descrizione)?void 0:a.toLowerCase().includes(E.toLowerCase()))||(null==(s=e.nome_cliente)?void 0:s.toLowerCase().includes(E.toLowerCase()))});return s?(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsx)(u.A,{className:"h-8 w-8 animate-spin"})}):(0,i.jsxs)("div",{className:"container mx-auto p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Gestione Cantieri"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Gestisci tutti i cantieri e accedi alle loro funzionalit\xe0"})]}),(0,i.jsxs)(m.lG,{open:S,onOpenChange:J,children:[(0,i.jsx)(m.zM,{asChild:!0,children:(0,i.jsxs)(c.$,{children:[(0,i.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,i.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,i.jsxs)(m.c7,{children:[(0,i.jsx)(m.L3,{children:"Crea Nuovo Cantiere"}),(0,i.jsx)(m.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,i.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(o.J,{htmlFor:"commessa",className:"text-right",children:"Commessa"}),(0,i.jsx)(l.p,{id:"commessa",value:D.commessa,onChange:e=>G({...D,commessa:e.target.value}),className:"col-span-3"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(o.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,i.jsx)(l.p,{id:"descrizione",value:D.descrizione,onChange:e=>G({...D,descrizione:e.target.value}),className:"col-span-3"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(o.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,i.jsx)(l.p,{id:"nome_cliente",value:D.nome_cliente,onChange:e=>G({...D,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(o.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,i.jsx)(l.p,{id:"password_cantiere",type:"password",value:D.password_cantiere,onChange:e=>G({...D,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,i.jsx)(m.Es,{children:(0,i.jsx)(c.$,{onClick:Z,children:"Crea Cantiere"})})]})]})]}),k&&(0,i.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(j.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,i.jsx)("span",{className:"text-red-800",children:k})]})}),(0,i.jsx)("div",{className:"mb-6",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(p.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,i.jsx)(l.p,{placeholder:"Cerca cantieri...",value:E,onChange:e=>M(e.target.value),className:"pl-8"})]})}),y?(0,i.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,i.jsx)(u.A,{className:"h-8 w-8 animate-spin"})}):0===B.length?(0,i.jsx)(r.Zp,{children:(0,i.jsxs)(r.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,i.jsx)(v.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Nessun cantiere trovato"}),(0,i.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:E?"Nessun cantiere corrisponde ai criteri di ricerca":"Crea il tuo primo cantiere per iniziare"}),!E&&(0,i.jsxs)(c.$,{onClick:()=>J(!0),children:[(0,i.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Crea Primo Cantiere"]})]})}):(0,i.jsx)(r.Zp,{children:(0,i.jsxs)(d.XI,{children:[(0,i.jsx)(d.A0,{children:(0,i.jsxs)(d.Hj,{children:[(0,i.jsx)(d.nd,{children:"Commessa"}),(0,i.jsx)(d.nd,{children:"Descrizione"}),(0,i.jsx)(d.nd,{children:"Cliente"}),(0,i.jsx)(d.nd,{children:"Data Creazione"}),(0,i.jsx)(d.nd,{children:"Codice"}),(0,i.jsx)(d.nd,{className:"text-right",children:"Azioni"})]})}),(0,i.jsx)(d.BF,{children:B.map(e=>(0,i.jsxs)(d.Hj,{children:[(0,i.jsx)(d.nA,{className:"font-medium",children:e.commessa}),(0,i.jsx)(d.nA,{children:e.descrizione}),(0,i.jsx)(d.nA,{children:e.nome_cliente}),(0,i.jsx)(d.nA,{children:new Date(e.data_creazione).toLocaleDateString()}),(0,i.jsx)(d.nA,{children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("code",{className:"text-sm bg-muted px-2 py-1 rounded",children:e.codice_univoco}),(0,i.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>q(e.codice_univoco),children:(0,i.jsx)(f,{className:"h-3 w-3"})})]})}),(0,i.jsx)(d.nA,{className:"text-right",children:(0,i.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,i.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>P(e),children:[(0,i.jsx)(N.A,{className:"mr-2 h-3 w-3"}),"Gestisci"]}),(0,i.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>V(e),children:(0,i.jsx)(_.A,{className:"h-3 w-3"})})]})})]},e.id_cantiere))})]})}),(0,i.jsx)(m.lG,{open:L,onOpenChange:F,children:(0,i.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,i.jsxs)(m.c7,{children:[(0,i.jsx)(m.L3,{children:"Modifica Cantiere"}),(0,i.jsx)(m.rr,{children:"Modifica i dettagli del cantiere selezionato"})]}),(0,i.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(o.J,{htmlFor:"edit-commessa",className:"text-right",children:"Commessa"}),(0,i.jsx)(l.p,{id:"edit-commessa",value:D.commessa,onChange:e=>G({...D,commessa:e.target.value}),className:"col-span-3"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(o.J,{htmlFor:"edit-descrizione",className:"text-right",children:"Descrizione"}),(0,i.jsx)(l.p,{id:"edit-descrizione",value:D.descrizione,onChange:e=>G({...D,descrizione:e.target.value}),className:"col-span-3"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(o.J,{htmlFor:"edit-nome_cliente",className:"text-right",children:"Cliente"}),(0,i.jsx)(l.p,{id:"edit-nome_cliente",value:D.nome_cliente,onChange:e=>G({...D,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(o.J,{htmlFor:"edit-indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,i.jsx)(l.p,{id:"edit-indirizzo_cantiere",value:D.indirizzo_cantiere,onChange:e=>G({...D,indirizzo_cantiere:e.target.value}),className:"col-span-3"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(o.J,{htmlFor:"edit-citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,i.jsx)(l.p,{id:"edit-citta_cantiere",value:D.citta_cantiere,onChange:e=>G({...D,citta_cantiere:e.target.value}),className:"col-span-3"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(o.J,{htmlFor:"edit-nazione_cantiere",className:"text-right",children:"Nazione"}),(0,i.jsx)(l.p,{id:"edit-nazione_cantiere",value:D.nazione_cantiere,onChange:e=>G({...D,nazione_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,i.jsxs)(m.Es,{children:[(0,i.jsx)(c.$,{variant:"outline",onClick:()=>F(!1),children:"Annulla"}),(0,i.jsx)(c.$,{onClick:H,children:"Salva Modifiche"})]})]})})]})}},85057:(e,a,s)=>{"use strict";s.d(a,{J:()=>r});var i=s(95155);s(12115);var t=s(40968),n=s(59434);function r(e){let{className:a,...s}=e;return(0,i.jsx)(t.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s})}}},e=>{var a=a=>e(e.s=a);e.O(0,[836,83,464,645,441,684,358],()=>a(40321)),_N_E=e.O()}]);