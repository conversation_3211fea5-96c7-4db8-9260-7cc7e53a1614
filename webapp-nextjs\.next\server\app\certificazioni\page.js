(()=>{var e={};e.id=992,e.ids=[992],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6211:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,BF:()=>l,Hj:()=>o,XI:()=>i,nA:()=>d,nd:()=>c});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function n({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10002:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["certificazioni",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,71792)),"C:\\CMS\\webapp-nextjs\\src\\app\\certificazioni\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\certificazioni\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/certificazioni/page",pathname:"/certificazioni",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16023:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},44765:(e,t,s)=>{Promise.resolve().then(s.bind(s,71792))},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51750:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(60687),a=s(43210),i=s(44493),n=s(29523),l=s(96834),o=s(89667),c=s(6211),d=s(63213);s(62185);var x=s(10022),u=s(31158),p=s(16023),m=s(96474),h=s(5336),f=s(93613),j=s(48730),v=s(99270),b=s(41862),g=s(13861),w=s(63143);let y=(0,s(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);function N(){let[e,t]=(0,a.useState)(""),[s,N]=(0,a.useState)("all"),[A,C]=(0,a.useState)([]),[k,z]=(0,a.useState)(!0),[_,q]=(0,a.useState)(""),{user:L,cantiere:M}=(0,d.A)(),P=e=>{switch(e?.toLowerCase()){case"conforme":case"pass":case"ok":return(0,r.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Conforme"});case"non conforme":case"fail":case"ko":return(0,r.jsx)(l.E,{className:"bg-red-100 text-red-800",children:"Non Conforme"});case"in corso":case"pending":return(0,r.jsx)(l.E,{className:"bg-yellow-100 text-yellow-800",children:"In Corso"});default:return(0,r.jsx)(l.E,{variant:"secondary",children:e||"Da Verificare"})}},S=A.filter(t=>{let r=t.id_cavo?.toLowerCase().includes(e.toLowerCase())||t.operatore?.toLowerCase().includes(e.toLowerCase())||t.strumento_utilizzato?.toLowerCase().includes(e.toLowerCase()),a=!0;if("all"!==s)switch(s){case"conforme":a=t.risultato?.toLowerCase().includes("conforme")||t.risultato?.toLowerCase()==="pass";break;case"non_conforme":a=t.risultato?.toLowerCase().includes("non conforme")||t.risultato?.toLowerCase()==="fail";break;case"in_corso":a=t.risultato?.toLowerCase().includes("corso")||t.risultato?.toLowerCase()==="pending"}return r&&a}),E={totali:A.length,conformi:A.filter(e=>e.risultato?.toLowerCase().includes("conforme")||e.risultato?.toLowerCase()==="pass").length,non_conformi:A.filter(e=>e.risultato?.toLowerCase().includes("non conforme")||e.risultato?.toLowerCase()==="fail").length,in_corso:A.filter(e=>e.risultato?.toLowerCase().includes("corso")||e.risultato?.toLowerCase()==="pending").length};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600"}),"Certificazioni"]}),(0,r.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione completa delle certificazioni e test dei cavi"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Esporta"]}),(0,r.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Importa"]}),(0,r.jsxs)(n.$,{size:"sm",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Nuova Certificazione"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:E.totali})]}),(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"Conformi"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:E.conformi})]}),(0,r.jsx)(h.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"Non Conformi"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-red-600",children:E.non_conformi})]}),(0,r.jsx)(f.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"In Corso"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:E.in_corso})]}),(0,r.jsx)(j.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(v.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(o.p,{placeholder:"Cerca per ID cavo, operatore o strumento...",value:e,onChange:e=>t(e.target.value),className:"w-full"})}),(0,r.jsx)("div",{className:"flex gap-2",children:["all","conforme","non_conforme","in_corso"].map(e=>(0,r.jsx)(n.$,{variant:s===e?"default":"outline",size:"sm",onClick:()=>N(e),children:"all"===e?"Tutte":"conforme"===e?"Conformi":"non_conforme"===e?"Non Conformi":"In Corso"},e))})]})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsxs)(i.ZB,{children:["Elenco Certificazioni (",S.length,")"]}),(0,r.jsx)(i.BT,{children:"Gestione completa delle certificazioni con risultati e dettagli tecnici"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(c.XI,{children:[(0,r.jsx)(c.A0,{children:(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nd,{children:"ID Cavo"}),(0,r.jsx)(c.nd,{children:"Data Certificazione"}),(0,r.jsx)(c.nd,{children:"Risultato"}),(0,r.jsx)(c.nd,{children:"Operatore"}),(0,r.jsx)(c.nd,{children:"Strumento"}),(0,r.jsx)(c.nd,{children:"Note"}),(0,r.jsx)(c.nd,{children:"Azioni"})]})}),(0,r.jsx)(c.BF,{children:k?(0,r.jsx)(c.Hj,{children:(0,r.jsx)(c.nA,{colSpan:7,className:"text-center py-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 animate-spin"}),"Caricamento certificazioni..."]})})}):_?(0,r.jsx)(c.Hj,{children:(0,r.jsx)(c.nA,{colSpan:7,className:"text-center py-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),_]})})}):0===S.length?(0,r.jsx)(c.Hj,{children:(0,r.jsx)(c.nA,{colSpan:7,className:"text-center py-8 text-slate-500",children:"Nessuna certificazione trovata"})}):S.map(e=>(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nA,{className:"font-medium",children:e.id_cavo}),(0,r.jsx)(c.nA,{children:new Date(e.data_certificazione).toLocaleDateString("it-IT")}),(0,r.jsx)(c.nA,{children:P(e.risultato)}),(0,r.jsx)(c.nA,{children:e.operatore||"-"}),(0,r.jsx)(c.nA,{children:e.strumento_utilizzato||"-"}),(0,r.jsx)(c.nA,{children:(0,r.jsx)("div",{className:"max-w-xs truncate",title:e.note,children:e.note||"-"})}),(0,r.jsx)(c.nA,{children:(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(y,{className:"h-4 w-4"})})]})})]},e.id_certificazione))})]})})})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56381:(e,t,s)=>{Promise.resolve().then(s.bind(s,51750))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71792:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\certificazioni\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\certificazioni\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(60687);s(43210);var a=s(4780);function i({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,341,658,692],()=>s(10002));module.exports=r})();