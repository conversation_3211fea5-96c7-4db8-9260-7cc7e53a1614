{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'\n\n// Configurazione base per l'API\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n\n// Crea istanza axios con configurazione base\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n})\n\n// Interceptor per aggiungere il token di autenticazione\napiClient.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('access_token')\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`\n    }\n    return config\n  },\n  (error) => {\n    return Promise.reject(error)\n  }\n)\n\n// Interceptor per gestire le risposte e gli errori\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token scaduto o non valido\n      localStorage.removeItem('access_token')\n      localStorage.removeItem('user_data')\n      window.location.href = '/login'\n    }\n    return Promise.reject(error)\n  }\n)\n\n// Tipi per le risposte API\nexport interface ApiResponse<T = any> {\n  data: T\n  message?: string\n  status: number\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[]\n  total: number\n  page: number\n  size: number\n  pages: number\n}\n\n// Funzioni helper per le chiamate API\nexport const api = {\n  // GET request\n  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.get<T>(url, config)\n    return response.data\n  },\n\n  // POST request\n  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.post<T>(url, data, config)\n    return response.data\n  },\n\n  // PUT request\n  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.put<T>(url, data, config)\n    return response.data\n  },\n\n  // PATCH request\n  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.patch<T>(url, data, config)\n    return response.data\n  },\n\n  // DELETE request\n  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.delete<T>(url, config)\n    return response.data\n  },\n}\n\n// Servizi API specifici per CABLYS\nexport const authApi = {\n  // Login utente - usa FormData per OAuth2PasswordRequestForm\n  login: async (credentials: { username: string; password: string }) => {\n    const formData = new FormData()\n    formData.append('username', credentials.username)\n    formData.append('password', credentials.password)\n\n    const response = await apiClient.post('/api/auth/login', formData, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    })\n    return response.data\n  },\n\n  // Login cantiere - usa JSON per CantiereLogin\n  loginCantiere: (credentials: { codice_cantiere: string; password_cantiere: string }) =>\n    api.post<{ access_token: string; token_type: string; cantiere: any }>('/api/auth/login/cantiere', {\n      codice_univoco: credentials.codice_cantiere,\n      password: credentials.password_cantiere\n    }),\n\n  // Verifica token\n  verifyToken: () =>\n    api.post<{ user: any }>('/api/auth/test-token'),\n\n  // Logout\n  logout: () => {\n    localStorage.removeItem('access_token')\n    localStorage.removeItem('user_data')\n    window.location.href = '/login'\n  }\n}\n\nexport const caviApi = {\n  // Ottieni tutti i cavi\n  getCavi: (cantiereId: number, params?: any) =>\n    api.get<any[]>(`/api/cavi/${cantiereId}`, { params }),\n\n  // Ottieni cavo specifico\n  getCavo: (cantiereId: number, idCavo: string) =>\n    api.get<any>(`/api/cavi/${cantiereId}/${idCavo}`),\n\n  // Crea nuovo cavo\n  createCavo: (cantiereId: number, cavo: any) =>\n    api.post<any>(`/api/cavi/${cantiereId}`, cavo),\n\n  // Aggiorna cavo\n  updateCavo: (cantiereId: number, idCavo: string, updates: any) =>\n    api.put<any>(`/api/cavi/${cantiereId}/${idCavo}`, updates),\n\n  // Elimina cavo\n  deleteCavo: (cantiereId: number, idCavo: string) =>\n    api.delete(`/api/cavi/${cantiereId}/${idCavo}`),\n\n  // Aggiorna metri posati\n  updateMetriPosati: (cantiereId: number, idCavo: string, metri: number) =>\n    api.patch<any>(`/api/cavi/${cantiereId}/${idCavo}/metri-posati`, { metri_posati: metri }),\n\n  // Aggiorna bobina\n  updateBobina: (cantiereId: number, idCavo: string, bobina: string) =>\n    api.patch<any>(`/api/cavi/${cantiereId}/${idCavo}/bobina`, { id_bobina: bobina }),\n\n  // Aggiorna collegamento\n  updateCollegamento: (cantiereId: number, idCavo: string, collegamento: number) =>\n    api.patch<any>(`/api/cavi/${cantiereId}/${idCavo}/collegamento`, { collegamenti: collegamento }),\n}\n\nexport const parcoCaviApi = {\n  // Ottieni tutte le bobine\n  getBobine: (cantiereId: number) =>\n    api.get<any[]>(`/api/parco-cavi/${cantiereId}`),\n\n  // Ottieni bobina specifica\n  getBobina: (cantiereId: number, idBobina: string) =>\n    api.get<any>(`/api/parco-cavi/${cantiereId}/${idBobina}`),\n\n  // Crea nuova bobina\n  createBobina: (cantiereId: number, bobina: any) =>\n    api.post<any>(`/api/parco-cavi/${cantiereId}`, bobina),\n\n  // Aggiorna bobina\n  updateBobina: (cantiereId: number, idBobina: string, updates: any) =>\n    api.put<any>(`/api/parco-cavi/${cantiereId}/${idBobina}`, updates),\n\n  // Elimina bobina\n  deleteBobina: (cantiereId: number, idBobina: string) =>\n    api.delete(`/api/parco-cavi/${cantiereId}/${idBobina}`),\n}\n\nexport const comandeApi = {\n  // Ottieni tutte le comande\n  getComande: (cantiereId: number) =>\n    api.get<any[]>(`/api/comande/${cantiereId}`),\n\n  // Ottieni comanda specifica\n  getComanda: (cantiereId: number, codiceComanda: string) =>\n    api.get<any>(`/api/comande/${cantiereId}/${codiceComanda}`),\n\n  // Crea nuova comanda\n  createComanda: (cantiereId: number, comanda: any) =>\n    api.post<any>(`/api/comande/${cantiereId}`, comanda),\n\n  // Aggiorna comanda\n  updateComanda: (cantiereId: number, codiceComanda: string, updates: any) =>\n    api.put<any>(`/api/comande/${cantiereId}/${codiceComanda}`, updates),\n\n  // Elimina comanda\n  deleteComanda: (cantiereId: number, codiceComanda: string) =>\n    api.delete(`/api/comande/${cantiereId}/${codiceComanda}`),\n\n  // Assegna cavi a comanda\n  assegnaCavi: (cantiereId: number, codiceComanda: string, caviIds: string[]) =>\n    api.post<any>(`/api/comande/${cantiereId}/${codiceComanda}/assegna-cavi`, { cavi_ids: caviIds }),\n}\n\nexport const responsabiliApi = {\n  // Ottieni tutti i responsabili\n  getResponsabili: (cantiereId: number) =>\n    api.get<any[]>(`/api/responsabili/${cantiereId}`),\n\n  // Crea nuovo responsabile\n  createResponsabile: (cantiereId: number, responsabile: any) =>\n    api.post<any>(`/api/responsabili/${cantiereId}`, responsabile),\n\n  // Aggiorna responsabile\n  updateResponsabile: (cantiereId: number, id: number, updates: any) =>\n    api.put<any>(`/api/responsabili/${cantiereId}/${id}`, updates),\n\n  // Elimina responsabile\n  deleteResponsabile: (cantiereId: number, id: number) =>\n    api.delete(`/api/responsabili/${cantiereId}/${id}`),\n}\n\nexport const reportsApi = {\n  // Report avanzamento\n  getReportAvanzamento: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/avanzamento`),\n\n  // Report BOQ\n  getReportBOQ: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/boq`),\n\n  // Report utilizzo bobine\n  getReportUtilizzoBobine: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/utilizzo-bobine`),\n\n  // Report progress\n  getReportProgress: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/progress`),\n}\n\nexport const cantieriApi = {\n  // Ottieni tutti i cantieri\n  getCantieri: () =>\n    api.get<any[]>('/api/cantieri'),\n\n  // Ottieni cantiere specifico\n  getCantiere: (id: number) =>\n    api.get<any>(`/api/cantieri/${id}`),\n\n  // Crea nuovo cantiere\n  createCantiere: (cantiere: any) =>\n    api.post<any>('/api/cantieri', cantiere),\n\n  // Aggiorna cantiere\n  updateCantiere: (id: number, updates: any) =>\n    api.put<any>(`/api/cantieri/${id}`, updates),\n}\n\nexport default apiClient\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAEA,gCAAgC;AAChC,MAAM,eAAe,6DAAmC;AAExD,6CAA6C;AAC7C,MAAM,YAA2B,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wDAAwD;AACxD,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,mDAAmD;AACnD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,6BAA6B;QAC7B,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAmBK,MAAM,MAAM;IACjB,cAAc;IACd,KAAK,OAAgB,KAAa;QAChC,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,KAAK;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,MAAM,OAAgB,KAAa,MAAY;QAC7C,MAAM,WAAW,MAAM,UAAU,IAAI,CAAI,KAAK,MAAM;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,KAAK,OAAgB,KAAa,MAAY;QAC5C,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,KAAK,MAAM;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,OAAO,OAAgB,KAAa,MAAY;QAC9C,MAAM,WAAW,MAAM,UAAU,KAAK,CAAI,KAAK,MAAM;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,QAAQ,OAAgB,KAAa;QACnC,MAAM,WAAW,MAAM,UAAU,MAAM,CAAI,KAAK;QAChD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4DAA4D;IAC5D,OAAO,OAAO;QACZ,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAChD,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAEhD,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,mBAAmB,UAAU;YACjE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,8CAA8C;IAC9C,eAAe,CAAC,cACd,IAAI,IAAI,CAA8D,4BAA4B;YAChG,gBAAgB,YAAY,eAAe;YAC3C,UAAU,YAAY,iBAAiB;QACzC;IAEF,iBAAiB;IACjB,aAAa,IACX,IAAI,IAAI,CAAgB;IAE1B,SAAS;IACT,QAAQ;QACN,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;AACF;AAEO,MAAM,UAAU;IACrB,uBAAuB;IACvB,SAAS,CAAC,YAAoB,SAC5B,IAAI,GAAG,CAAQ,CAAC,UAAU,EAAE,YAAY,EAAE;YAAE;QAAO;IAErD,yBAAyB;IACzB,SAAS,CAAC,YAAoB,SAC5B,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ;IAElD,kBAAkB;IAClB,YAAY,CAAC,YAAoB,OAC/B,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,YAAY,EAAE;IAE3C,gBAAgB;IAChB,YAAY,CAAC,YAAoB,QAAgB,UAC/C,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ,EAAE;IAEpD,eAAe;IACf,YAAY,CAAC,YAAoB,SAC/B,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ;IAEhD,wBAAwB;IACxB,mBAAmB,CAAC,YAAoB,QAAgB,QACtD,IAAI,KAAK,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YAAE,cAAc;QAAM;IAEzF,kBAAkB;IAClB,cAAc,CAAC,YAAoB,QAAgB,SACjD,IAAI,KAAK,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE;YAAE,WAAW;QAAO;IAEjF,wBAAwB;IACxB,oBAAoB,CAAC,YAAoB,QAAgB,eACvD,IAAI,KAAK,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YAAE,cAAc;QAAa;AAClG;AAEO,MAAM,eAAe;IAC1B,0BAA0B;IAC1B,WAAW,CAAC,aACV,IAAI,GAAG,CAAQ,CAAC,gBAAgB,EAAE,YAAY;IAEhD,2BAA2B;IAC3B,WAAW,CAAC,YAAoB,WAC9B,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU;IAE1D,oBAAoB;IACpB,cAAc,CAAC,YAAoB,SACjC,IAAI,IAAI,CAAM,CAAC,gBAAgB,EAAE,YAAY,EAAE;IAEjD,kBAAkB;IAClB,cAAc,CAAC,YAAoB,UAAkB,UACnD,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU,EAAE;IAE5D,iBAAiB;IACjB,cAAc,CAAC,YAAoB,WACjC,IAAI,MAAM,CAAC,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU;AAC1D;AAEO,MAAM,aAAa;IACxB,2BAA2B;IAC3B,YAAY,CAAC,aACX,IAAI,GAAG,CAAQ,CAAC,aAAa,EAAE,YAAY;IAE7C,4BAA4B;IAC5B,YAAY,CAAC,YAAoB,gBAC/B,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,CAAC,EAAE,eAAe;IAE5D,qBAAqB;IACrB,eAAe,CAAC,YAAoB,UAClC,IAAI,IAAI,CAAM,CAAC,aAAa,EAAE,YAAY,EAAE;IAE9C,mBAAmB;IACnB,eAAe,CAAC,YAAoB,eAAuB,UACzD,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,CAAC,EAAE,eAAe,EAAE;IAE9D,kBAAkB;IAClB,eAAe,CAAC,YAAoB,gBAClC,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,EAAE,eAAe;IAE1D,yBAAyB;IACzB,aAAa,CAAC,YAAoB,eAAuB,UACvD,IAAI,IAAI,CAAM,CAAC,aAAa,EAAE,WAAW,CAAC,EAAE,cAAc,aAAa,CAAC,EAAE;YAAE,UAAU;QAAQ;AAClG;AAEO,MAAM,kBAAkB;IAC7B,+BAA+B;IAC/B,iBAAiB,CAAC,aAChB,IAAI,GAAG,CAAQ,CAAC,kBAAkB,EAAE,YAAY;IAElD,0BAA0B;IAC1B,oBAAoB,CAAC,YAAoB,eACvC,IAAI,IAAI,CAAM,CAAC,kBAAkB,EAAE,YAAY,EAAE;IAEnD,wBAAwB;IACxB,oBAAoB,CAAC,YAAoB,IAAY,UACnD,IAAI,GAAG,CAAM,CAAC,kBAAkB,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE;IAExD,uBAAuB;IACvB,oBAAoB,CAAC,YAAoB,KACvC,IAAI,MAAM,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,EAAE,IAAI;AACtD;AAEO,MAAM,aAAa;IACxB,qBAAqB;IACrB,sBAAsB,CAAC,aACrB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,YAAY,CAAC;IAEvD,aAAa;IACb,cAAc,CAAC,aACb,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,IAAI,CAAC;IAE/C,yBAAyB;IACzB,yBAAyB,CAAC,aACxB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,gBAAgB,CAAC;IAE3D,kBAAkB;IAClB,mBAAmB,CAAC,aAClB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,SAAS,CAAC;AACtD;AAEO,MAAM,cAAc;IACzB,2BAA2B;IAC3B,aAAa,IACX,IAAI,GAAG,CAAQ;IAEjB,6BAA6B;IAC7B,aAAa,CAAC,KACZ,IAAI,GAAG,CAAM,CAAC,cAAc,EAAE,IAAI;IAEpC,sBAAsB;IACtB,gBAAgB,CAAC,WACf,IAAI,IAAI,CAAM,iBAAiB;IAEjC,oBAAoB;IACpB,gBAAgB,CAAC,IAAY,UAC3B,IAAI,GAAG,CAAM,CAAC,cAAc,EAAE,IAAI,EAAE;AACxC;uCAEe", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\nimport { User, Cantier<PERSON> } from '@/types'\nimport { authApi } from '@/lib/api'\n\ninterface AuthContextType {\n  user: User | null\n  cantiere: Cantiere | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  login: (username: string, password: string) => Promise<void>\n  loginCantiere: (codice_cantiere: string, password_cantiere: string) => Promise<void>\n  logout: () => void\n  checkAuth: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [cantiere, setCantiere] = useState<Cantiere | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  const isAuthenticated = !!user || !!cantiere\n\n  // Verifica l'autenticazione al caricamento\n  useEffect(() => {\n    checkAuth()\n  }, [])\n\n  const checkAuth = async () => {\n    try {\n      const token = localStorage.getItem('access_token')\n      if (!token) {\n        setIsLoading(false)\n        return\n      }\n\n      const response = await authApi.verifyToken()\n\n      // Il backend restituisce i dati dell'utente direttamente\n      if (response.role === 'cantieri_user') {\n        // È un login cantiere - ricostruisci i dati del cantiere\n        const cantiereData = {\n          id_cantiere: response.cantiere_id || 0,\n          commessa: response.cantiere_name || response.username,\n          codice_univoco: '',\n          id_utente: response.user_id\n        }\n        setCantiere(cantiereData)\n        setUser(null)\n      } else {\n        // È un login utente normale\n        const userData = {\n          id_utente: response.user_id,\n          username: response.username,\n          ruolo: response.role\n        }\n        setUser(userData)\n        setCantiere(null)\n      }\n    } catch (error) {\n      console.error('Errore verifica autenticazione:', error)\n      localStorage.removeItem('access_token')\n      localStorage.removeItem('user_data')\n      localStorage.removeItem('cantiere_data')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const login = async (username: string, password: string) => {\n    try {\n      setIsLoading(true)\n      const response = await authApi.login({ username, password })\n\n      localStorage.setItem('access_token', response.access_token)\n\n      // Salva il token anche nei cookie per il middleware\n      document.cookie = `access_token=${response.access_token}; path=/; max-age=${24 * 60 * 60}` // 24 ore\n\n      // Il backend restituisce i dati dell'utente direttamente nella risposta\n      const userData = {\n        id_utente: response.user_id,\n        username: response.username,\n        ruolo: response.role\n      }\n\n      localStorage.setItem('user_data', JSON.stringify(userData))\n      setUser(userData)\n      setCantiere(null)\n    } catch (error) {\n      console.error('Errore login:', error)\n      throw error\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loginCantiere = async (codice_cantiere: string, password_cantiere: string) => {\n    try {\n      setIsLoading(true)\n      const response = await authApi.loginCantiere({ codice_cantiere, password_cantiere })\n\n      localStorage.setItem('access_token', response.access_token)\n\n      // Salva il token anche nei cookie per il middleware\n      document.cookie = `access_token=${response.access_token}; path=/; max-age=${24 * 60 * 60}` // 24 ore\n\n      // Il backend restituisce i dati del cantiere direttamente nella risposta\n      const cantiereData = {\n        id_cantiere: response.cantiere_id,\n        commessa: response.cantiere_name,\n        codice_univoco: codice_cantiere,\n        id_utente: response.user_id\n      }\n\n      localStorage.setItem('cantiere_data', JSON.stringify(cantiereData))\n      setCantiere(cantiereData)\n      setUser(null)\n    } catch (error) {\n      console.error('Errore login cantiere:', error)\n      throw error\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const logout = () => {\n    localStorage.removeItem('access_token')\n    localStorage.removeItem('user_data')\n    localStorage.removeItem('cantiere_data')\n\n    // Rimuovi il token anche dai cookie\n    document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'\n\n    setUser(null)\n    setCantiere(null)\n    window.location.href = '/login'\n  }\n\n  const value: AuthContextType = {\n    user,\n    cantiere,\n    isAuthenticated,\n    isLoading,\n    login,\n    loginCantiere,\n    logout,\n    checkAuth,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAiBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC;IAEpC,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,aAAa;gBACb;YACF;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW;YAE1C,yDAAyD;YACzD,IAAI,SAAS,IAAI,KAAK,iBAAiB;gBACrC,yDAAyD;gBACzD,MAAM,eAAe;oBACnB,aAAa,SAAS,WAAW,IAAI;oBACrC,UAAU,SAAS,aAAa,IAAI,SAAS,QAAQ;oBACrD,gBAAgB;oBAChB,WAAW,SAAS,OAAO;gBAC7B;gBACA,YAAY;gBACZ,QAAQ;YACV,OAAO;gBACL,4BAA4B;gBAC5B,MAAM,WAAW;oBACf,WAAW,SAAS,OAAO;oBAC3B,UAAU,SAAS,QAAQ;oBAC3B,OAAO,SAAS,IAAI;gBACtB;gBACA,QAAQ;gBACR,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,QAAQ,OAAO,UAAkB;QACrC,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAAE;gBAAU;YAAS;YAE1D,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;YAE1D,oDAAoD;YACpD,SAAS,MAAM,GAAG,CAAC,aAAa,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,KAAK,KAAK,IAAI,CAAC,SAAS;;YAEpG,wEAAwE;YACxE,MAAM,WAAW;gBACf,WAAW,SAAS,OAAO;gBAC3B,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,IAAI;YACtB;YAEA,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACjD,QAAQ;YACR,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,OAAO,iBAAyB;QACpD,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,aAAa,CAAC;gBAAE;gBAAiB;YAAkB;YAElF,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;YAE1D,oDAAoD;YACpD,SAAS,MAAM,GAAG,CAAC,aAAa,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,KAAK,KAAK,IAAI,CAAC,SAAS;;YAEpG,yEAAyE;YACzE,MAAM,eAAe;gBACnB,aAAa,SAAS,WAAW;gBACjC,UAAU,SAAS,aAAa;gBAChC,gBAAgB;gBAChB,WAAW,SAAS,OAAO;YAC7B;YAEA,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YACrD,YAAY;YACZ,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,oCAAoC;QACpC,SAAS,MAAM,GAAG;QAElB,QAAQ;QACR,YAAY;QACZ,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { useAuth } from '@/contexts/AuthContext'\nimport {\n  Cable,\n  Home,\n  Activity,\n  BarChart3,\n  Settings,\n  Users,\n  Menu,\n  X,\n  Building2,\n  ClipboardList,\n  FileText,\n  LogOut,\n  Package\n} from 'lucide-react'\n\nconst getNavigation = (userRole: string | undefined) => {\n  const baseNavigation = [\n    { name: 'Dashboard', href: '/', icon: Home },\n  ]\n\n  if (userRole === 'owner') {\n    return [\n      ...baseNavigation,\n      { name: 'Amministrazione', href: '/admin', icon: Settings },\n      { name: 'Cantier<PERSON>', href: '/cantieri', icon: Building2 },\n    ]\n  }\n\n  if (userRole === 'user') {\n    return [\n      ...baseNavigation,\n      { name: '<PERSON><PERSON><PERSON>', href: '/cantieri', icon: Building2 },\n    ]\n  }\n\n  if (userRole === 'cantieri_user') {\n    return [\n      { name: 'Gestione Cavi', href: '/cavi', icon: Cable },\n      { name: '<PERSON><PERSON><PERSON>', href: '/parco-cavi', icon: Package },\n      { name: 'Comande', href: '/comande', icon: ClipboardList },\n      { name: 'Certificazioni', href: '/certificazioni', icon: FileText },\n      { name: 'Report', href: '/reports', icon: BarChart3 },\n      { name: 'Produttività', href: '/productivity', icon: Activity },\n    ]\n  }\n\n  // Default navigation for authenticated users\n  return [\n    ...baseNavigation,\n    { name: 'Gestione Cavi', href: '/cavi', icon: Cable },\n    { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },\n    { name: 'Comande', href: '/comande', icon: ClipboardList },\n    { name: 'Certificazioni', href: '/certificazioni', icon: FileText },\n    { name: 'Report', href: '/reports', icon: BarChart3 },\n    { name: 'Produttività', href: '/productivity', icon: Activity },\n  ]\n}\n\nexport function Navbar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, cantiere, isAuthenticated, logout } = useAuth()\n  const navigation = getNavigation(user?.ruolo)\n\n  // Non mostrare navbar nella pagina di login\n  if (pathname === '/login') {\n    return null\n  }\n\n  // Se non autenticato, non mostrare navbar\n  if (!isAuthenticated) {\n    return null\n  }\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          \n          {/* Logo e Brand */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center\">\n                <Cable className=\"w-5 h-5 text-white\" />\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold text-slate-900\">CABLYS</h1>\n                <p className=\"text-xs text-slate-500 -mt-1\">Cable Installation System</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Navigation Desktop */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href || \n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n              \n              return (\n                <Link key={item.name} href={item.href}>\n                  <Button\n                    variant={isActive ? \"default\" : \"ghost\"}\n                    size=\"sm\"\n                    className={`flex items-center space-x-2 ${\n                      isActive \n                        ? 'bg-blue-600 text-white hover:bg-blue-700' \n                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'\n                    }`}\n                  >\n                    <Icon className=\"w-4 h-4\" />\n                    <span className=\"hidden lg:inline\">{item.name}</span>\n                  </Button>\n                </Link>\n              )\n            })}\n          </div>\n\n          {/* User Info e Mobile Menu */}\n          <div className=\"flex items-center space-x-4\">\n            \n            {/* User Info */}\n            <div className=\"hidden sm:flex items-center space-x-3\">\n              <div className=\"text-right\">\n                <p className=\"text-sm font-medium text-slate-900\">\n                  {user ? user.username : cantiere?.commessa}\n                </p>\n                <p className=\"text-xs text-slate-500\">\n                  {user ? user.ruolo : 'Cantiere'}\n                </p>\n              </div>\n              <div className=\"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\">\n                {user ? <Users className=\"w-4 h-4 text-white\" /> : <Building2 className=\"w-4 h-4 text-white\" />}\n              </div>\n              <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800\">\n                Online\n              </Badge>\n              <Button variant=\"ghost\" size=\"sm\" onClick={logout}>\n                <LogOut className=\"w-4 h-4\" />\n              </Button>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"text-slate-600\"\n              >\n                {isOpen ? <X className=\"w-5 h-5\" /> : <Menu className=\"w-5 h-5\" />}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isOpen && (\n        <div className=\"md:hidden border-t border-slate-200 bg-white\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href || \n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n              \n              return (\n                <Link key={item.name} href={item.href}>\n                  <Button\n                    variant={isActive ? \"default\" : \"ghost\"}\n                    size=\"sm\"\n                    className={`w-full justify-start space-x-3 ${\n                      isActive \n                        ? 'bg-blue-600 text-white' \n                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'\n                    }`}\n                    onClick={() => setIsOpen(false)}\n                  >\n                    <Icon className=\"w-4 h-4\" />\n                    <span>{item.name}</span>\n                  </Button>\n                </Link>\n              )\n            })}\n          </div>\n          \n          {/* Mobile User Info */}\n          <div className=\"border-t border-slate-200 px-4 py-3\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\">\n                <Users className=\"w-4 h-4 text-white\" />\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-slate-900\">Admin User</p>\n                <p className=\"text-xs text-slate-500\">Cantiere Demo</p>\n              </div>\n              <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800 ml-auto\">\n                Online\n              </Badge>\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAwBA,MAAM,gBAAgB,CAAC;IACrB,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAa,MAAM;YAAK,MAAM,mMAAA,CAAA,OAAI;QAAC;KAC5C;IAED,IAAI,aAAa,SAAS;QACxB,OAAO;eACF;YACH;gBAAE,MAAM;gBAAmB,MAAM;gBAAU,MAAM,0MAAA,CAAA,WAAQ;YAAC;YAC1D;gBAAE,MAAM;gBAAY,MAAM;gBAAa,MAAM,gNAAA,CAAA,YAAS;YAAC;SACxD;IACH;IAEA,IAAI,aAAa,QAAQ;QACvB,OAAO;eACF;YACH;gBAAE,MAAM;gBAAY,MAAM;gBAAa,MAAM,gNAAA,CAAA,YAAS;YAAC;SACxD;IACH;IAEA,IAAI,aAAa,iBAAiB;QAChC,OAAO;YACL;gBAAE,MAAM;gBAAiB,MAAM;gBAAS,MAAM,oMAAA,CAAA,QAAK;YAAC;YACpD;gBAAE,MAAM;gBAAc,MAAM;gBAAe,MAAM,wMAAA,CAAA,UAAO;YAAC;YACzD;gBAAE,MAAM;gBAAW,MAAM;gBAAY,MAAM,wNAAA,CAAA,gBAAa;YAAC;YACzD;gBAAE,MAAM;gBAAkB,MAAM;gBAAmB,MAAM,8MAAA,CAAA,WAAQ;YAAC;YAClE;gBAAE,MAAM;gBAAU,MAAM;gBAAY,MAAM,kNAAA,CAAA,YAAS;YAAC;YACpD;gBAAE,MAAM;gBAAgB,MAAM;gBAAiB,MAAM,0MAAA,CAAA,WAAQ;YAAC;SAC/D;IACH;IAEA,6CAA6C;IAC7C,OAAO;WACF;QACH;YAAE,MAAM;YAAiB,MAAM;YAAS,MAAM,oMAAA,CAAA,QAAK;QAAC;QACpD;YAAE,MAAM;YAAc,MAAM;YAAe,MAAM,wMAAA,CAAA,UAAO;QAAC;QACzD;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,wNAAA,CAAA,gBAAa;QAAC;QACzD;YAAE,MAAM;YAAkB,MAAM;YAAmB,MAAM,8MAAA,CAAA,WAAQ;QAAC;QAClE;YAAE,MAAM;YAAU,MAAM;YAAY,MAAM,kNAAA,CAAA,YAAS;QAAC;QACpD;YAAE,MAAM;YAAgB,MAAM;YAAiB,MAAM,0MAAA,CAAA,WAAQ;QAAC;KAC/D;AACH;AAEO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1D,MAAM,aAAa,cAAc,MAAM;IAEvC,4CAA4C;IAC5C,IAAI,aAAa,UAAU;QACzB,OAAO;IACT;IAEA,0CAA0C;IAC1C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;;;sCAMlD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;gCAClE,MAAM,OAAO,KAAK,IAAI;gCAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAAiB,MAAM,KAAK,IAAI;8CACnC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,WAAW,YAAY;wCAChC,MAAK;wCACL,WAAW,CAAC,4BAA4B,EACtC,WACI,6CACA,0DACJ;;0DAEF,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAoB,KAAK,IAAI;;;;;;;;;;;;mCAXtC,KAAK,IAAI;;;;;4BAexB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,OAAO,KAAK,QAAQ,GAAG,UAAU;;;;;;8DAEpC,8OAAC;oDAAE,WAAU;8DACV,OAAO,KAAK,KAAK,GAAG;;;;;;;;;;;;sDAGzB,8OAAC;4CAAI,WAAU;sDACZ,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;qEAA0B,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAE1E,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAA8B;;;;;;sDAGnE,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,SAAS;sDACzC,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKtB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,UAAU,CAAC;wCAC1B,WAAU;kDAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ/D,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;4BAClE,MAAM,OAAO,KAAK,IAAI;4BAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,WAAW,YAAY;oCAChC,MAAK;oCACL,WAAW,CAAC,+BAA+B,EACzC,WACI,2BACA,0DACJ;oCACF,SAAS,IAAM,UAAU;;sDAEzB,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;;;;;;+BAZT,KAAK,IAAI;;;;;wBAgBxB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;8CAExC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzF", "debugId": null}}]}