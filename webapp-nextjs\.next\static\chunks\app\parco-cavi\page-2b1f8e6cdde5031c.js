(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[562],{13717:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},24944:(e,t,s)=>{"use strict";s.d(t,{k:()=>l});var i=s(95155);s(12115);var a=s(55863),r=s(59434);function l(e){let{className:t,value:s,...l}=e;return(0,i.jsx)(a.bL,{"data-slot":"progress",className:(0,r.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...l,children:(0,i.jsx)(a.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var i=s(95155);s(12115);var a=s(99708),r=s(74466),l=s(59434);let n=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,asChild:r=!1,...c}=e,d=r?a.DX:"span";return(0,i.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(n({variant:s}),t),...c})}},27212:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var i=s(95155),a=s(12115),r=s(66695),l=s(30285),n=s(26126),c=s(62523),d=s(85127),o=s(24944),u=s(40283),x=s(25731),m=s(37108),h=s(91788),p=s(29869),v=s(84616),j=s(40646),f=s(14186),b=s(85339),y=s(47924),g=s(51154),N=s(92657),w=s(13717),A=s(62525);function k(){let[e,t]=(0,a.useState)(""),[s,k]=(0,a.useState)("all"),[_,C]=(0,a.useState)([]),[z,M]=(0,a.useState)(!0),[E,$]=(0,a.useState)(""),{user:S,cantiere:L}=(0,u.A)();(0,a.useEffect)(()=>{P()},[]);let P=async()=>{try{M(!0),$("");let e=(null==L?void 0:L.id_cantiere)||(null==S?void 0:S.id_utente);if(!e)return void $("Cantiere non selezionato");let t=await x.Fw.getBobine(e);C(t)}catch(s){var e,t;console.error("Errore caricamento bobine:",s),$((null==(t=s.response)||null==(e=t.data)?void 0:e.detail)||"Errore durante il caricamento delle bobine")}finally{M(!1)}};(0,a.useEffect)(()=>{let e=setTimeout(()=>{P()},500);return()=>clearTimeout(e)},[e,s]);let I=(e,t,s)=>{let a=s>0?t/s*100:0;return 0===a?(0,i.jsx)(n.E,{className:"bg-red-100 text-red-800",children:"Esaurita"}):a<20?(0,i.jsx)(n.E,{className:"bg-orange-100 text-orange-800",children:"Quasi Esaurita"}):a<50?(0,i.jsx)(n.E,{className:"bg-yellow-100 text-yellow-800",children:"In Uso"}):(0,i.jsx)(n.E,{className:"bg-green-100 text-green-800",children:"Disponibile"})},H=_.filter(t=>{var i,a,r,l;let n=(null==(i=t.id_bobina)?void 0:i.toLowerCase().includes(e.toLowerCase()))||(null==(a=t.numero_bobina)?void 0:a.toLowerCase().includes(e.toLowerCase()))||(null==(r=t.tipologia)?void 0:r.toLowerCase().includes(e.toLowerCase()))||(null==(l=t.utility)?void 0:l.toLowerCase().includes(e.toLowerCase())),c=!0;if("all"!==s){let e=t.metri_totali>0?t.metri_residui/t.metri_totali*100:0;switch(s){case"disponibile":c=e>=50;break;case"in_uso":c=e>0&&e<50;break;case"esaurita":c=0===e}}return n&&c}),T={totali:_.length,disponibili:_.filter(e=>e.metri_totali>0&&e.metri_residui/e.metri_totali>=.5).length,in_uso:_.filter(e=>{let t=e.metri_totali>0?e.metri_residui/e.metri_totali:0;return t>0&&t<.5}).length,esaurite:_.filter(e=>0===e.metri_residui).length};return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,i.jsx)(m.A,{className:"h-8 w-8 text-blue-600"}),"Parco Cavi"]}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione completa delle bobine e materiali"})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Esporta"]}),(0,i.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,i.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Importa"]}),(0,i.jsxs)(l.$,{size:"sm",children:[(0,i.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Nuova Bobina"]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:T.totali})]}),(0,i.jsx)(m.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Disponibili"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-green-600",children:T.disponibili})]}),(0,i.jsx)(j.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"In Uso"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:T.in_uso})]}),(0,i.jsx)(f.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Esaurite"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-red-600",children:T.esaurite})]}),(0,i.jsx)(b.A,{className:"h-8 w-8 text-red-500"})]})})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsx)(r.aR,{children:(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(y.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsx)(c.p,{placeholder:"Cerca per ID bobina, numero, tipologia o utility...",value:e,onChange:e=>t(e.target.value),className:"w-full"})}),(0,i.jsx)("div",{className:"flex gap-2",children:["all","disponibile","in_uso","esaurita"].map(e=>(0,i.jsx)(l.$,{variant:s===e?"default":"outline",size:"sm",onClick:()=>k(e),children:"all"===e?"Tutte":"disponibile"===e?"Disponibili":"in_uso"===e?"In Uso":"Esaurite"},e))})]})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{children:["Elenco Bobine (",H.length,")"]}),(0,i.jsx)(r.BT,{children:"Gestione completa delle bobine con stato utilizzo e metrature"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)("div",{className:"rounded-md border",children:(0,i.jsxs)(d.XI,{children:[(0,i.jsx)(d.A0,{children:(0,i.jsxs)(d.Hj,{children:[(0,i.jsx)(d.nd,{children:"ID Bobina"}),(0,i.jsx)(d.nd,{children:"Numero"}),(0,i.jsx)(d.nd,{children:"Utility"}),(0,i.jsx)(d.nd,{children:"Tipologia"}),(0,i.jsx)(d.nd,{children:"Conduttori/Sezione"}),(0,i.jsx)(d.nd,{children:"Metrature"}),(0,i.jsx)(d.nd,{children:"Utilizzo"}),(0,i.jsx)(d.nd,{children:"Stato"}),(0,i.jsx)(d.nd,{children:"Ubicazione"}),(0,i.jsx)(d.nd,{children:"Azioni"})]})}),(0,i.jsx)(d.BF,{children:z?(0,i.jsx)(d.Hj,{children:(0,i.jsx)(d.nA,{colSpan:10,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)(g.A,{className:"h-4 w-4 animate-spin"}),"Caricamento bobine..."]})})}):E?(0,i.jsx)(d.Hj,{children:(0,i.jsx)(d.nA,{colSpan:10,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,i.jsx)(b.A,{className:"h-4 w-4"}),E]})})}):0===H.length?(0,i.jsx)(d.Hj,{children:(0,i.jsx)(d.nA,{colSpan:10,className:"text-center py-8 text-slate-500",children:"Nessuna bobina trovata"})}):H.map(e=>{let t=e.metri_totali>0?(e.metri_totali-e.metri_residui)/e.metri_totali*100:0;return(0,i.jsxs)(d.Hj,{children:[(0,i.jsx)(d.nA,{className:"font-medium",children:e.id_bobina}),(0,i.jsx)(d.nA,{children:e.numero_bobina||"-"}),(0,i.jsx)(d.nA,{children:e.utility||"-"}),(0,i.jsx)(d.nA,{children:e.tipologia||"-"}),(0,i.jsx)(d.nA,{children:(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsx)("div",{children:e.n_conduttori||"-"}),(0,i.jsx)("div",{className:"text-slate-500",children:e.sezione||"-"})]})}),(0,i.jsx)(d.nA,{children:(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsxs)("div",{children:["Residui: ",(0,i.jsxs)("span",{className:"font-medium",children:[e.metri_residui,"m"]})]}),(0,i.jsxs)("div",{className:"text-slate-500",children:["Totali: ",e.metri_totali,"m"]})]})}),(0,i.jsx)(d.nA,{children:(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"text-sm font-medium",children:[Math.round(t),"%"]}),(0,i.jsx)(o.k,{value:t,className:"h-2"})]})}),(0,i.jsx)(d.nA,{children:I(e.stato_bobina,e.metri_residui,e.metri_totali)}),(0,i.jsx)(d.nA,{children:(0,i.jsx)(n.E,{variant:"outline",children:e.ubicazione_bobina||"Non specificata"})}),(0,i.jsx)(d.nA,{children:(0,i.jsxs)("div",{className:"flex gap-1",children:[(0,i.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,i.jsx)(N.A,{className:"h-4 w-4"})}),(0,i.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,i.jsx)(w.A,{className:"h-4 w-4"})}),(0,i.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,i.jsx)(A.A,{className:"h-4 w-4"})})]})})]},e.id_bobina)})})]})})})]})]})})}},29869:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},30557:(e,t,s)=>{Promise.resolve().then(s.bind(s,27212))},37108:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},46081:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,q:()=>r});var i=s(12115),a=s(95155);function r(e,t){let s=i.createContext(t),r=e=>{let{children:t,...r}=e,l=i.useMemo(()=>r,Object.values(r));return(0,a.jsx)(s.Provider,{value:l,children:t})};return r.displayName=e+"Provider",[r,function(a){let r=i.useContext(s);if(r)return r;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function l(e,t=[]){let s=[],r=()=>{let t=s.map(e=>i.createContext(e));return function(s){let a=s?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return r.scopeName=e,[function(t,r){let l=i.createContext(r),n=s.length;s=[...s,r];let c=t=>{let{scope:s,children:r,...c}=t,d=s?.[e]?.[n]||l,o=i.useMemo(()=>c,Object.values(c));return(0,a.jsx)(d.Provider,{value:o,children:r})};return c.displayName=t+"Provider",[c,function(s,a){let c=a?.[e]?.[n]||l,d=i.useContext(c);if(d)return d;if(void 0!==r)return r;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=s.reduce((t,{useScope:s,scopeName:i})=>{let a=s(e)[`__scope${i}`];return{...t,...a}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return s.scopeName=t.scopeName,s}(r,...t)]}},47924:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},51154:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55863:(e,t,s)=>{"use strict";s.d(t,{C1:()=>g,bL:()=>y});var i=s(12115),a=s(46081),r=s(63655),l=s(95155),n="Progress",[c,d]=(0,a.A)(n),[o,u]=c(n),x=i.forwardRef((e,t)=>{var s,i,a,n;let{__scopeProgress:c,value:d=null,max:u,getValueLabel:x=p,...m}=e;(u||0===u)&&!f(u)&&console.error((s="".concat(u),i="Progress","Invalid prop `max` of value `".concat(s,"` supplied to `").concat(i,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let h=f(u)?u:100;null===d||b(d,h)||console.error((a="".concat(d),n="Progress","Invalid prop `value` of value `".concat(a,"` supplied to `").concat(n,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let y=b(d,h)?d:null,g=j(y)?x(y,h):void 0;return(0,l.jsx)(o,{scope:c,value:y,max:h,children:(0,l.jsx)(r.sG.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":j(y)?y:void 0,"aria-valuetext":g,role:"progressbar","data-state":v(y,h),"data-value":null!=y?y:void 0,"data-max":h,...m,ref:t})})});x.displayName=n;var m="ProgressIndicator",h=i.forwardRef((e,t)=>{var s;let{__scopeProgress:i,...a}=e,n=u(m,i);return(0,l.jsx)(r.sG.div,{"data-state":v(n.value,n.max),"data-value":null!=(s=n.value)?s:void 0,"data-max":n.max,...a,ref:t})});function p(e,t){return"".concat(Math.round(e/t*100),"%")}function v(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function j(e){return"number"==typeof e}function f(e){return j(e)&&!isNaN(e)&&e>0}function b(e,t){return j(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=m;var y=x,g=h},62525:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63655:(e,t,s)=>{"use strict";s.d(t,{hO:()=>c,sG:()=>n});var i=s(12115),a=s(47650),r=s(99708),l=s(95155),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,r.TL)(`Primitive.${t}`),a=i.forwardRef((e,i)=>{let{asChild:a,...r}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a?s:t,{...r,ref:i})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function c(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},84616:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[836,83,645,441,684,358],()=>t(30557)),_N_E=e.O()}]);