{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "DmFlnj8I3P1ljkWGhQVgM+UNdCE3hT3/mJhLA6xJySg=", "__NEXT_PREVIEW_MODE_ID": "a56832b51d69f234bf4a54b8e28571fc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bd27ff3e8458e6391bf17f1c1b097584956a650641c97aa9898a2017a14be89a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "233422f1a8cf02bbdc8ce73ea7dbef2f4ed6bbb4606f4744cce98a3ce67ab62d"}}}, "sortedMiddleware": ["/"], "functions": {}}