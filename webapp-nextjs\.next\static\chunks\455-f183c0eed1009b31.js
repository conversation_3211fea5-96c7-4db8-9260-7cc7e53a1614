"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[455],{3493:(e,a,t)=>{t.d(a,{A:()=>o});let o=(0,t(19946).A)("cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]])},23227:(e,a,t)=>{t.d(a,{A:()=>o});let o=(0,t(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},25731:(e,a,t)=>{t.d(a,{AR:()=>d,At:()=>i,CV:()=>s,FH:()=>r,Fw:()=>c,ZQ:()=>n,_I:()=>u,ug:()=>l});let o=t(23464).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let a=localStorage.getItem("access_token");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var a;return(null==(a=e.response)?void 0:a.status)===401&&(localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"),Promise.reject(e)});let r={get:async(e,a)=>(await o.get(e,a)).data,post:async(e,a,t)=>(await o.post(e,a,t)).data,put:async(e,a,t)=>(await o.put(e,a,t)).data,patch:async(e,a,t)=>(await o.patch(e,a,t)).data,delete:async(e,a)=>(await o.delete(e,a)).data},n={login:async e=>{let a=new FormData;return a.append("username",e.username),a.append("password",e.password),(await o.post("/api/auth/login",a,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>r.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>r.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},i={getCavi:(e,a)=>r.get("/api/cavi/".concat(e),{params:a}),getCavo:(e,a)=>r.get("/api/cavi/".concat(e,"/").concat(a)),createCavo:(e,a)=>r.post("/api/cavi/".concat(e),a),updateCavo:(e,a,t)=>r.put("/api/cavi/".concat(e,"/").concat(a),t),deleteCavo:(e,a)=>r.delete("/api/cavi/".concat(e,"/").concat(a)),updateMetriPosati:(e,a,t)=>r.patch("/api/cavi/".concat(e,"/").concat(a,"/metri-posati"),{metri_posati:t}),updateBobina:(e,a,t)=>r.patch("/api/cavi/".concat(e,"/").concat(a,"/bobina"),{id_bobina:t}),updateCollegamento:(e,a,t)=>r.patch("/api/cavi/".concat(e,"/").concat(a,"/collegamento"),{collegamenti:t})},c={getBobine:e=>r.get("/api/parco-cavi/".concat(e)),getBobina:(e,a)=>r.get("/api/parco-cavi/".concat(e,"/").concat(a)),createBobina:(e,a)=>r.post("/api/parco-cavi/".concat(e),a),updateBobina:(e,a,t)=>r.put("/api/parco-cavi/".concat(e,"/").concat(a),t),deleteBobina:(e,a)=>r.delete("/api/parco-cavi/".concat(e,"/").concat(a))},s={getComande:e=>r.get("/api/comande/".concat(e)),getComanda:(e,a)=>r.get("/api/comande/".concat(e,"/").concat(a)),createComanda:(e,a)=>r.post("/api/comande/".concat(e),a),updateComanda:(e,a,t)=>r.put("/api/comande/".concat(e,"/").concat(a),t),deleteComanda:(e,a)=>r.delete("/api/comande/".concat(e,"/").concat(a)),assegnaCavi:(e,a,t)=>r.post("/api/comande/".concat(e,"/").concat(a,"/assegna-cavi"),{cavi_ids:t})},d={getResponsabili:e=>r.get("/api/responsabili/".concat(e)),createResponsabile:(e,a)=>r.post("/api/responsabili/".concat(e),a),updateResponsabile:(e,a,t)=>r.put("/api/responsabili/".concat(e,"/").concat(a),t),deleteResponsabile:(e,a)=>r.delete("/api/responsabili/".concat(e,"/").concat(a))},l={getReportAvanzamento:e=>r.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>r.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>r.get("/api/reports/".concat(e,"/utilizzo-bobine")),getReportProgress:e=>r.get("/api/reports/".concat(e,"/progress"))},u={getCantieri:()=>r.get("/api/cantieri"),getCantiere:e=>r.get("/api/cantieri/".concat(e)),createCantiere:e=>r.post("/api/cantieri",e),updateCantiere:(e,a)=>r.put("/api/cantieri/".concat(e),a)}},26126:(e,a,t)=>{t.d(a,{E:()=>s});var o=t(95155);t(12115);var r=t(99708),n=t(74466),i=t(59434);let c=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function s(e){let{className:a,variant:t,asChild:n=!1,...s}=e,d=n?r.DX:"span";return(0,o.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(c({variant:t}),a),...s})}},30285:(e,a,t)=>{t.d(a,{$:()=>s});var o=t(95155);t(12115);var r=t(99708),n=t(74466),i=t(59434);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function s(e){let{className:a,variant:t,size:n,asChild:s=!1,...d}=e,l=s?r.DX:"button";return(0,o.jsx)(l,{"data-slot":"button",className:(0,i.cn)(c({variant:t,size:n,className:a})),...d})}},35695:(e,a,t)=>{var o=t(18999);t.o(o,"useParams")&&t.d(a,{useParams:function(){return o.useParams}}),t.o(o,"usePathname")&&t.d(a,{usePathname:function(){return o.usePathname}}),t.o(o,"useRouter")&&t.d(a,{useRouter:function(){return o.useRouter}})},40283:(e,a,t)=>{t.d(a,{A:()=>c,AuthProvider:()=>s});var o=t(95155),r=t(12115),n=t(25731);let i=(0,r.createContext)(void 0);function c(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function s(e){let{children:a}=e,[t,c]=(0,r.useState)(null),[s,d]=(0,r.useState)(null),[l,u]=(0,r.useState)(!0);(0,r.useEffect)(()=>{p()},[]);let p=async()=>{try{if(!localStorage.getItem("access_token"))return void u(!1);let e=await n.ZQ.verifyToken();if("cantieri_user"===e.role){let a={id_cantiere:e.cantiere_id||0,commessa:e.cantiere_name||e.username,codice_univoco:"",id_utente:e.user_id};d(a),c(null)}else{let a={id_utente:e.user_id,username:e.username,ruolo:e.role};c(a),d(null)}}catch(e){console.error("Errore verifica autenticazione:",e),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data")}finally{u(!1)}},g=async(e,a)=>{try{u(!0);let t=await n.ZQ.login({username:e,password:a});localStorage.setItem("access_token",t.access_token),document.cookie="access_token=".concat(t.access_token,"; path=/; max-age=").concat(86400);let o={id_utente:t.user_id,username:t.username,ruolo:t.role};localStorage.setItem("user_data",JSON.stringify(o)),c(o),d(null)}catch(e){throw console.error("Errore login:",e),e}finally{u(!1)}},v=async(e,a)=>{try{u(!0);let t=await n.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:a});localStorage.setItem("access_token",t.access_token),document.cookie="access_token=".concat(t.access_token,"; path=/; max-age=").concat(86400);let o={id_cantiere:t.cantiere_id,commessa:t.cantiere_name,codice_univoco:e,id_utente:t.user_id};localStorage.setItem("cantiere_data",JSON.stringify(o)),d(o),c(null)}catch(e){throw console.error("Errore login cantiere:",e),e}finally{u(!1)}};return(0,o.jsx)(i.Provider,{value:{user:t,cantiere:s,isAuthenticated:!!t||!!s,isLoading:l,login:g,loginCantiere:v,logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),document.cookie="access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",c(null),d(null),window.location.href="/login"},checkAuth:p},children:a})}},51154:(e,a,t)=>{t.d(a,{A:()=>o});let o=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,a,t)=>{t.d(a,{cn:()=>n});var o=t(52596),r=t(39688);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,o.$)(a))}},66695:(e,a,t)=>{t.d(a,{BT:()=>s,Wu:()=>d,ZB:()=>c,Zp:()=>n,aR:()=>i});var o=t(95155);t(12115);var r=t(59434);function n(e){let{className:a,...t}=e;return(0,o.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function i(e){let{className:a,...t}=e;return(0,o.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function c(e){let{className:a,...t}=e;return(0,o.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function s(e){let{className:a,...t}=e;return(0,o.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}function d(e){let{className:a,...t}=e;return(0,o.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}},71007:(e,a,t)=>{t.d(a,{A:()=>o});let o=(0,t(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},85339:(e,a,t)=>{t.d(a,{A:()=>o});let o=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}}]);