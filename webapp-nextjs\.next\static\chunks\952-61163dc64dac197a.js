"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[952],{3493:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]])},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},29869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},40646:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},62525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},85713:(e,t,n)=>{n.d(t,{UC:()=>t8,In:()=>t3,q7:()=>nt,VF:()=>nr,p4:()=>nn,ZL:()=>t7,bL:()=>t9,wn:()=>nl,PP:()=>no,l9:()=>t6,WT:()=>t4,LM:()=>ne});var r,o=n(12115),l=n(47650);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var a=n(85185);function s(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function u(e,t){var n=s(e,t,"get");return n.get?n.get.call(e):n.value}function c(e,t,n){var r=s(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var d=n(46081),f=n(6101),p=n(99708),h=n(95155),m=new WeakMap;function v(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=g(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function g(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap;var y=o.createContext(void 0),w=n(19178),x=n(92293),b=n(25519),S=n(61285);let C=["top","right","bottom","left"],R=Math.min,A=Math.max,k=Math.round,T=Math.floor,j=e=>({x:e,y:e}),E={left:"right",right:"left",bottom:"top",top:"bottom"},P={start:"end",end:"start"};function M(e,t){return"function"==typeof e?e(t):e}function L(e){return e.split("-")[0]}function N(e){return e.split("-")[1]}function D(e){return"x"===e?"y":"x"}function H(e){return"y"===e?"height":"width"}function O(e){return["top","bottom"].includes(L(e))?"y":"x"}function I(e){return e.replace(/start|end/g,e=>P[e])}function V(e){return e.replace(/left|right|bottom|top/g,e=>E[e])}function B(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function F(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function W(e,t,n){let r,{reference:o,floating:l}=e,i=O(t),a=D(O(t)),s=H(a),u=L(t),c="y"===i,d=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,p=o[s]/2-l[s]/2;switch(u){case"top":r={x:d,y:o.y-l.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-l.width,y:f};break;default:r={x:o.x,y:o.y}}switch(N(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let _=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=W(u,r,s),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:l,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[l]:{...p[l],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=W(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function z(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=M(t,e),h=B(p),m=a[f?"floating"===d?"reference":"floating":d],v=F(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(m)))||n?m:m.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),g="floating"===d?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),w=await (null==l.isElement?void 0:l.isElement(y))&&await (null==l.getScale?void 0:l.getScale(y))||{x:1,y:1},x=F(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function G(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function K(e){return C.some(t=>e[t]>=0)}async function q(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=L(n),a=N(n),s="y"===O(n),u=["left","top"].includes(i)?-1:1,c=l&&s?-1:1,d=M(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),s?{x:p*c,y:f*u}:{x:f*u,y:p*c}}function U(){return"undefined"!=typeof window}function Y(e){return $(e)?(e.nodeName||"").toLowerCase():"#document"}function X(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function Z(e){var t;return null==(t=($(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function $(e){return!!U()&&(e instanceof Node||e instanceof X(e).Node)}function J(e){return!!U()&&(e instanceof Element||e instanceof X(e).Element)}function Q(e){return!!U()&&(e instanceof HTMLElement||e instanceof X(e).HTMLElement)}function ee(e){return!!U()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof X(e).ShadowRoot)}function et(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ei(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function en(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function er(e){let t=eo(),n=J(e)?ei(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function eo(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function el(e){return["html","body","#document"].includes(Y(e))}function ei(e){return X(e).getComputedStyle(e)}function ea(e){return J(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function es(e){if("html"===Y(e))return e;let t=e.assignedSlot||e.parentNode||ee(e)&&e.host||Z(e);return ee(t)?t.host:t}function eu(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=es(t);return el(n)?t.ownerDocument?t.ownerDocument.body:t.body:Q(n)&&et(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),i=X(o);if(l){let e=ec(i);return t.concat(i,i.visualViewport||[],et(o)?o:[],e&&n?eu(e):[])}return t.concat(o,eu(o,[],n))}function ec(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ed(e){let t=ei(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=Q(e),l=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=k(n)!==l||k(r)!==i;return a&&(n=l,r=i),{width:n,height:r,$:a}}function ef(e){return J(e)?e:e.contextElement}function ep(e){let t=ef(e);if(!Q(t))return j(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=ed(t),i=(l?k(n.width):n.width)/r,a=(l?k(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),a&&Number.isFinite(a)||(a=1),{x:i,y:a}}let eh=j(0);function em(e){let t=X(e);return eo()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eh}function ev(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=ef(e),a=j(1);t&&(r?J(r)&&(a=ep(r)):a=ep(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===X(i))&&o)?em(i):j(0),u=(l.left+s.x)/a.x,c=(l.top+s.y)/a.y,d=l.width/a.x,f=l.height/a.y;if(i){let e=X(i),t=r&&J(r)?X(r):r,n=e,o=ec(n);for(;o&&r&&t!==n;){let e=ep(o),t=o.getBoundingClientRect(),r=ei(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,f*=e.y,u+=l,c+=i,o=ec(n=X(o))}}return F({width:d,height:f,x:u,y:c})}function eg(e,t){let n=ea(e).scrollLeft;return t?t.left+n:ev(Z(e)).left+n}function ey(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eg(e,r)),y:r.top+t.scrollTop}}function ew(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=X(e),r=Z(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,s=0;if(o){l=o.width,i=o.height;let e=eo();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:l,height:i,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=Z(e),n=ea(e),r=e.ownerDocument.body,o=A(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=A(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),i=-n.scrollLeft+eg(e),a=-n.scrollTop;return"rtl"===ei(r).direction&&(i+=A(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:i,y:a}}(Z(e));else if(J(t))r=function(e,t){let n=ev(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=Q(e)?ep(e):j(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=em(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return F(r)}function ex(e){return"static"===ei(e).position}function eb(e,t){if(!Q(e)||"fixed"===ei(e).position)return null;if(t)return t(e);let n=e.offsetParent;return Z(e)===n&&(n=n.ownerDocument.body),n}function eS(e,t){let n=X(e);if(en(e))return n;if(!Q(e)){let t=es(e);for(;t&&!el(t);){if(J(t)&&!ex(t))return t;t=es(t)}return n}let r=eb(e,t);for(;r&&["table","td","th"].includes(Y(r))&&ex(r);)r=eb(r,t);return r&&el(r)&&ex(r)&&!er(r)?n:r||function(e){let t=es(e);for(;Q(t)&&!el(t);){if(er(t))return t;if(en(t))break;t=es(t)}return null}(e)||n}let eC=async function(e){let t=this.getOffsetParent||eS,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=Q(t),o=Z(t),l="fixed"===n,i=ev(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=j(0);if(r||!r&&!l)if(("body"!==Y(t)||et(o))&&(a=ea(t)),r){let e=ev(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eg(o));l&&!r&&o&&(s.x=eg(o));let u=!o||r||l?j(0):ey(o,a);return{x:i.left+a.scrollLeft-s.x-u.x,y:i.top+a.scrollTop-s.y-u.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eR={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=Z(r),a=!!t&&en(t.floating);if(r===i||a&&l)return n;let s={scrollLeft:0,scrollTop:0},u=j(1),c=j(0),d=Q(r);if((d||!d&&!l)&&(("body"!==Y(r)||et(i))&&(s=ea(r)),Q(r))){let e=ev(r);u=ep(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let f=!i||d||l?j(0):ey(i,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+f.x,y:n.y*u.y-s.scrollTop*u.y+c.y+f.y}},getDocumentElement:Z,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?en(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eu(e,[],!1).filter(e=>J(e)&&"body"!==Y(e)),o=null,l="fixed"===ei(e).position,i=l?es(e):e;for(;J(i)&&!el(i);){let t=ei(i),n=er(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||et(i)&&!n&&function e(t,n){let r=es(t);return!(r===n||!J(r)||el(r))&&("fixed"===ei(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=es(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],i=l[0],a=l.reduce((e,n)=>{let r=ew(t,n,o);return e.top=A(r.top,e.top),e.right=R(r.right,e.right),e.bottom=R(r.bottom,e.bottom),e.left=A(r.left,e.left),e},ew(t,i,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eS,getElementRects:eC,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ed(e);return{width:t,height:n}},getScale:ep,isElement:J,isRTL:function(e){return"rtl"===ei(e).direction}};function eA(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ek=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:i,elements:a,middlewareData:s}=t,{element:u,padding:c=0}=M(e,t)||{};if(null==u)return{};let d=B(c),f={x:n,y:r},p=D(O(o)),h=H(p),m=await i.getDimensions(u),v="y"===p,g=v?"clientHeight":"clientWidth",y=l.reference[h]+l.reference[p]-f[p]-l.floating[h],w=f[p]-l.reference[p],x=await (null==i.getOffsetParent?void 0:i.getOffsetParent(u)),b=x?x[g]:0;b&&await (null==i.isElement?void 0:i.isElement(x))||(b=a.floating[g]||l.floating[h]);let S=b/2-m[h]/2-1,C=R(d[v?"top":"left"],S),k=R(d[v?"bottom":"right"],S),T=b-m[h]-k,j=b/2-m[h]/2+(y/2-w/2),E=A(C,R(j,T)),P=!s.arrow&&null!=N(o)&&j!==E&&l.reference[h]/2-(j<C?C:k)-m[h]/2<0,L=P?j<C?j-C:j-T:0;return{[p]:f[p]+L,data:{[p]:E,centerOffset:j-E-L,...P&&{alignmentOffset:L}},reset:P}}}),eT=(e,t,n)=>{let r=new Map,o={platform:eR,...n},l={...o.platform,_c:r};return _(e,t,{...o,platform:l})};var ej="undefined"!=typeof document?o.useLayoutEffect:function(){};function eE(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eE(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eE(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eP(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eM(e,t){let n=eP(e);return Math.round(t*n)/n}function eL(e){let t=o.useRef(e);return ej(()=>{t.current=e}),t}let eN=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ek({element:n.current,padding:r}).fn(t):{}:n?ek({element:n,padding:r}).fn(t):{}}}),eD=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,s=await q(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:i}}}}}(e),options:[e,t]}),eH=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:i=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=M(e,t),u={x:n,y:r},c=await z(t,s),d=O(L(o)),f=D(d),p=u[f],h=u[d];if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+c[e],r=p-c[t];p=A(n,R(p,r))}if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+c[e],r=h-c[t];h=A(n,R(h,r))}let m=a.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:l,[d]:i}}}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=M(e,t),c={x:n,y:r},d=O(o),f=D(d),p=c[f],h=c[d],m=M(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===f?"height":"width",t=l.reference[f]-l.floating[e]+v.mainAxis,n=l.reference[f]+l.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var g,y;let e="y"===f?"width":"height",t=["top","left"].includes(L(o)),n=l.reference[d]-l.floating[e]+(t&&(null==(g=i.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=l.reference[d]+l.reference[e]+(t?0:(null==(y=i.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),eI=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=M(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let x=L(a),b=O(c),S=L(c)===c,C=await (null==d.isRTL?void 0:d.isRTL(f.floating)),R=m||(S||!y?[V(c)]:function(e){let t=V(e);return[I(e),t,I(t)]}(c)),A="none"!==g;!m&&A&&R.push(...function(e,t,n,r){let o=N(e),l=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(L(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(I)))),l}(c,y,g,C));let k=[c,...R],T=await z(t,w),j=[],E=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&j.push(T[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=N(e),o=D(O(e)),l=H(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=V(i)),[i,V(i)]}(a,u,C);j.push(T[e[0]],T[e[1]])}if(E=[...E,{placement:a,overflows:j}],!j.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=k[e];if(t&&("alignment"!==h||b===O(t)||E.every(e=>e.overflows[0]>0&&O(e.placement)===b)))return{data:{index:e,overflows:E},reset:{placement:t}};let n=null==(l=E.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(v){case"bestFit":{let e=null==(i=E.filter(e=>{if(A){let t=O(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eV=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:i,rects:a,platform:s,elements:u}=t,{apply:c=()=>{},...d}=M(e,t),f=await z(t,d),p=L(i),h=N(i),m="y"===O(i),{width:v,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,l=h===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(l=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,w=v-f.left-f.right,x=R(g-f[o],y),b=R(v-f[l],w),S=!t.middlewareData.shift,C=x,k=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(C=y),S&&!h){let e=A(f.left,0),t=A(f.right,0),n=A(f.top,0),r=A(f.bottom,0);m?k=v-2*(0!==e||0!==t?e+t:A(f.left,f.right)):C=g-2*(0!==n||0!==r?n+r:A(f.top,f.bottom))}await c({...t,availableWidth:k,availableHeight:C});let T=await s.getDimensions(u.floating);return v!==T.width||g!==T.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eB=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=M(e,t);switch(r){case"referenceHidden":{let e=G(await z(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:K(e)}}}case"escaped":{let e=G(await z(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:K(e)}}}default:return{}}}}}(e),options:[e,t]}),eF=(e,t)=>({...eN(e),options:[e,t]});var eW=n(63655),e_=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,h.jsx)(eW.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,h.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e_.displayName="Arrow";var ez=n(39033),eG=n(52712),eK="Popper",[eq,eU]=(0,d.A)(eK),[eY,eX]=eq(eK),eZ=e=>{let{__scopePopper:t,children:n}=e,[r,l]=o.useState(null);return(0,h.jsx)(eY,{scope:t,anchor:r,onAnchorChange:l,children:n})};eZ.displayName=eK;var e$="PopperAnchor",eJ=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...l}=e,i=eX(e$,n),a=o.useRef(null),s=(0,f.s)(t,a);return o.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,h.jsx)(eW.sG.div,{...l,ref:s})});eJ.displayName=e$;var eQ="PopperContent",[e0,e1]=eq(eQ),e2=o.forwardRef((e,t)=>{var n,r,i,a,s,u,c,d;let{__scopePopper:p,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:S=0,sticky:C="partial",hideWhenDetached:k=!1,updatePositionStrategy:j="optimized",onPlaced:E,...P}=e,M=eX(eQ,p),[L,N]=o.useState(null),D=(0,f.s)(t,e=>N(e)),[H,O]=o.useState(null),I=function(e){let[t,n]=o.useState(void 0);return(0,eG.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(H),V=null!=(c=null==I?void 0:I.width)?c:0,B=null!=(d=null==I?void 0:I.height)?d:0,F="number"==typeof S?S:{top:0,right:0,bottom:0,left:0,...S},W=Array.isArray(b)?b:[b],_=W.length>0,z={padding:F,boundary:W.filter(e4),altBoundary:_},{refs:G,floatingStyles:K,placement:q,isPositioned:U,middlewareData:Y}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:i,elements:{reference:a,floating:s}={},transform:u=!0,whileElementsMounted:c,open:d}=e,[f,p]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=o.useState(r);eE(h,r)||m(r);let[v,g]=o.useState(null),[y,w]=o.useState(null),x=o.useCallback(e=>{e!==R.current&&(R.current=e,g(e))},[]),b=o.useCallback(e=>{e!==A.current&&(A.current=e,w(e))},[]),S=a||v,C=s||y,R=o.useRef(null),A=o.useRef(null),k=o.useRef(f),T=null!=c,j=eL(c),E=eL(i),P=eL(d),M=o.useCallback(()=>{if(!R.current||!A.current)return;let e={placement:t,strategy:n,middleware:h};E.current&&(e.platform=E.current),eT(R.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};L.current&&!eE(k.current,t)&&(k.current=t,l.flushSync(()=>{p(t)}))})},[h,t,n,E,P]);ej(()=>{!1===d&&k.current.isPositioned&&(k.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let L=o.useRef(!1);ej(()=>(L.current=!0,()=>{L.current=!1}),[]),ej(()=>{if(S&&(R.current=S),C&&(A.current=C),S&&C){if(j.current)return j.current(S,C,M);M()}},[S,C,M,j,T]);let N=o.useMemo(()=>({reference:R,floating:A,setReference:x,setFloating:b}),[x,b]),D=o.useMemo(()=>({reference:S,floating:C}),[S,C]),H=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=eM(D.floating,f.x),r=eM(D.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eP(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,D.floating,f.x,f.y]);return o.useMemo(()=>({...f,update:M,refs:N,elements:D,floatingStyles:H}),[f,M,N,D,H])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=ef(e),d=l||i?[...c?eu(c):[],...eu(t)]:[];d.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)});let f=c&&s?function(e,t){let n,r=null,o=Z(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function i(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),l();let u=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=u;if(a||t(),!f||!p)return;let h=T(d),m=T(o.clientWidth-(c+f)),v={rootMargin:-h+"px "+-m+"px "+-T(o.clientHeight-(d+p))+"px "+-T(c)+"px",threshold:A(0,R(1,s))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==s){if(!g)return i();r?i(!1,r):n=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==r||eA(u,e.getBoundingClientRect())||i(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),l}(c,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!u&&h.observe(c),h.observe(t));let m=u?ev(e):null;return u&&function t(){let r=ev(e);m&&!eA(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{l&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,u&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===j})},elements:{reference:M.anchor},middleware:[eD({mainAxis:v+B,alignmentAxis:y}),x&&eH({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?eO():void 0,...z}),x&&eI({...z}),eV({...z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),H&&eF({element:H,padding:w}),e3({arrowWidth:V,arrowHeight:B}),k&&eB({strategy:"referenceHidden",...z})]}),[X,$]=e7(q),J=(0,ez.c)(E);(0,eG.N)(()=>{U&&(null==J||J())},[U,J]);let Q=null==(n=Y.arrow)?void 0:n.x,ee=null==(r=Y.arrow)?void 0:r.y,et=(null==(i=Y.arrow)?void 0:i.centerOffset)!==0,[en,er]=o.useState();return(0,eG.N)(()=>{L&&er(window.getComputedStyle(L).zIndex)},[L]),(0,h.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:U?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(a=Y.transformOrigin)?void 0:a.x,null==(s=Y.transformOrigin)?void 0:s.y].join(" "),...(null==(u=Y.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,h.jsx)(e0,{scope:p,placedSide:X,onArrowChange:O,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,h.jsx)(eW.sG.div,{"data-side":X,"data-align":$,...P,ref:D,style:{...P.style,animation:U?void 0:"none"}})})})});e2.displayName=eQ;var e5="PopperArrow",e9={top:"bottom",right:"left",bottom:"top",left:"right"},e6=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e1(e5,n),l=e9[o.placedSide];return(0,h.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,h.jsx)(e_,{...r,ref:t,style:{...r.style,display:"block"}})})});function e4(e){return null!==e}e6.displayName=e5;var e3=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=e7(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(l=null==(r=u.arrow)?void 0:r.x)?l:0)+d/2,g=(null!=(i=null==(o=u.arrow)?void 0:o.y)?i:0)+f/2,y="",w="";return"bottom"===p?(y=c?m:"".concat(v,"px"),w="".concat(-f,"px")):"top"===p?(y=c?m:"".concat(v,"px"),w="".concat(s.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=c?m:"".concat(g,"px")):"left"===p&&(y="".concat(s.floating.width+f,"px"),w=c?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function e7(e){let[t,n="center"]=e.split("-");return[t,n]}var e8=n(34378),te=n(5845),tt=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});o.forwardRef((e,t)=>(0,h.jsx)(eW.sG.span,{...e,ref:t,style:{...tt,...e.style}})).displayName="VisuallyHidden";var tn=n(38168),tr=n(93795),to=[" ","Enter","ArrowUp","ArrowDown"],tl=[" ","Enter"],ti="Select",[ta,ts,tu]=function(e){let t=e+"CollectionProvider",[n,r]=(0,d.A)(t),[l,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=o.useRef(null),i=o.useRef(new Map).current;return(0,h.jsx)(l,{scope:t,itemMap:i,collectionRef:r,children:n})};a.displayName=t;let s=e+"CollectionSlot",u=(0,p.TL)(s),c=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(s,n),l=(0,f.s)(t,o.collectionRef);return(0,h.jsx)(u,{ref:l,children:r})});c.displayName=s;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,p.TL)(m),y=o.forwardRef((e,t)=>{let{scope:n,children:r,...l}=e,a=o.useRef(null),s=(0,f.s)(t,a),u=i(m,n);return o.useEffect(()=>(u.itemMap.set(a,{ref:a,...l}),()=>void u.itemMap.delete(a))),(0,h.jsx)(g,{...{[v]:""},ref:s,children:r})});return y.displayName=m,[{Provider:a,Slot:c,ItemSlot:y},function(t){let n=i(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(ti),[tc,td]=(0,d.A)(ti,[tu,eU]),tf=eU(),[tp,th]=tc(ti),[tm,tv]=tc(ti),tg=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:l,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:c,name:d,autoComplete:f,disabled:p,required:m,form:v}=e,g=tf(t),[w,x]=o.useState(null),[b,C]=o.useState(null),[R,A]=o.useState(!1),k=function(e){let t=o.useContext(y);return e||t||"ltr"}(c),[T,j]=(0,te.i)({prop:r,defaultProp:null!=l&&l,onChange:i,caller:ti}),[E,P]=(0,te.i)({prop:a,defaultProp:s,onChange:u,caller:ti}),M=o.useRef(null),L=!w||v||!!w.closest("form"),[N,D]=o.useState(new Set),H=Array.from(N).map(e=>e.props.value).join(";");return(0,h.jsx)(eZ,{...g,children:(0,h.jsxs)(tp,{required:m,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:C,valueNodeHasChildren:R,onValueNodeHasChildrenChange:A,contentId:(0,S.B)(),value:E,onValueChange:P,open:T,onOpenChange:j,dir:k,triggerPointerDownPosRef:M,disabled:p,children:[(0,h.jsx)(ta.Provider,{scope:t,children:(0,h.jsx)(tm,{scope:e.__scopeSelect,onNativeOptionAdd:o.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:o.useCallback(e=>{D(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),L?(0,h.jsxs)(t0,{"aria-hidden":!0,required:m,tabIndex:-1,name:d,autoComplete:f,value:E,onChange:e=>P(e.target.value),disabled:p,form:v,children:[void 0===E?(0,h.jsx)("option",{value:""}):null,Array.from(N)]},H):null]})})};tg.displayName=ti;var ty="SelectTrigger",tw=o.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...l}=e,i=tf(n),s=th(ty,n),u=s.disabled||r,c=(0,f.s)(t,s.onTriggerChange),d=ts(n),p=o.useRef("touch"),[m,v,g]=t2(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=t5(t,e,n);void 0!==r&&s.onValueChange(r.value)}),y=e=>{u||(s.onOpenChange(!0),g()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,h.jsx)(eJ,{asChild:!0,...i,children:(0,h.jsx)(eW.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":t1(s.value)?"":void 0,...l,ref:c,onClick:(0,a.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&y(e)}),onPointerDown:(0,a.m)(l.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(l.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&to.includes(e.key)&&(y(),e.preventDefault())})})})});tw.displayName=ty;var tx="SelectValue",tb=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,s=th(tx,n),{onValueNodeHasChildrenChange:u}=s,c=void 0!==l,d=(0,f.s)(t,s.onValueNodeChange);return(0,eG.N)(()=>{u(c)},[u,c]),(0,h.jsx)(eW.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:t1(s.value)?(0,h.jsx)(h.Fragment,{children:i}):l})});tb.displayName=tx;var tS=o.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,h.jsx)(eW.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tS.displayName="SelectIcon";var tC=e=>(0,h.jsx)(e8.Z,{asChild:!0,...e});tC.displayName="SelectPortal";var tR="SelectContent",tA=o.forwardRef((e,t)=>{let n=th(tR,e.__scopeSelect),[r,i]=o.useState();return((0,eG.N)(()=>{i(new DocumentFragment)},[]),n.open)?(0,h.jsx)(tE,{...e,ref:t}):r?l.createPortal((0,h.jsx)(tk,{scope:e.__scopeSelect,children:(0,h.jsx)(ta.Slot,{scope:e.__scopeSelect,children:(0,h.jsx)("div",{children:e.children})})}),r):null});tA.displayName=tR;var[tk,tT]=tc(tR),tj=(0,p.TL)("SelectContent.RemoveScroll"),tE=o.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:s,side:u,sideOffset:c,align:d,alignOffset:p,arrowPadding:m,collisionBoundary:v,collisionPadding:g,sticky:y,hideWhenDetached:S,avoidCollisions:C,...R}=e,A=th(tR,n),[k,T]=o.useState(null),[j,E]=o.useState(null),P=(0,f.s)(t,e=>T(e)),[M,L]=o.useState(null),[N,D]=o.useState(null),H=ts(n),[O,I]=o.useState(!1),V=o.useRef(!1);o.useEffect(()=>{if(k)return(0,tn.Eq)(k)},[k]),(0,x.Oh)();let B=o.useCallback(e=>{let[t,...n]=H().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&j&&(j.scrollTop=0),n===r&&j&&(j.scrollTop=j.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[H,j]),F=o.useCallback(()=>B([M,k]),[B,M,k]);o.useEffect(()=>{O&&F()},[O,F]);let{onOpenChange:W,triggerPointerDownPosRef:_}=A;o.useEffect(()=>{if(k){let e={x:0,y:0},t=t=>{var n,r,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=_.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(r=_.current)?void 0:r.y)?l:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():k.contains(n.target)||W(!1),document.removeEventListener("pointermove",t),_.current=null};return null!==_.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[k,W,_]),o.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[z,G]=t2(e=>{let t=H().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=t5(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),K=o.useCallback((e,t,n)=>{let r=!V.current&&!n;(void 0!==A.value&&A.value===t||r)&&(L(e),r&&(V.current=!0))},[A.value]),q=o.useCallback(()=>null==k?void 0:k.focus(),[k]),U=o.useCallback((e,t,n)=>{let r=!V.current&&!n;(void 0!==A.value&&A.value===t||r)&&D(e)},[A.value]),Y="popper"===r?tM:tP,X=Y===tM?{side:u,sideOffset:c,align:d,alignOffset:p,arrowPadding:m,collisionBoundary:v,collisionPadding:g,sticky:y,hideWhenDetached:S,avoidCollisions:C}:{};return(0,h.jsx)(tk,{scope:n,content:k,viewport:j,onViewportChange:E,itemRefCallback:K,selectedItem:M,onItemLeave:q,itemTextRefCallback:U,focusSelectedItem:F,selectedItemText:N,position:r,isPositioned:O,searchRef:z,children:(0,h.jsx)(tr.A,{as:tj,allowPinchZoom:!0,children:(0,h.jsx)(b.n,{asChild:!0,trapped:A.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(l,e=>{var t;null==(t=A.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,h.jsx)(w.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>A.onOpenChange(!1),children:(0,h.jsx)(Y,{role:"listbox",id:A.contentId,"data-state":A.open?"open":"closed",dir:A.dir,onContextMenu:e=>e.preventDefault(),...R,...X,onPlaced:()=>I(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,a.m)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=H().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});tE.displayName="SelectContentImpl";var tP=o.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...l}=e,a=th(tR,n),s=tT(tR,n),[u,c]=o.useState(null),[d,p]=o.useState(null),m=(0,f.s)(t,e=>p(e)),v=ts(n),g=o.useRef(!1),y=o.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:b,focusSelectedItem:S}=s,C=o.useCallback(()=>{if(a.trigger&&a.valueNode&&u&&d&&w&&x&&b){let e=a.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==a.dir){let r=o.left-t.left,l=n.left-r,a=e.left-l,s=e.width+a,c=Math.max(s,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=s+"px",u.style.left=d+"px"}else{let r=t.right-o.right,l=window.innerWidth-n.right-r,a=window.innerWidth-e.right-l,s=e.width+a,c=Math.max(s,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=s+"px",u.style.right=d+"px"}let l=v(),s=window.innerHeight-20,c=w.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),h=parseInt(f.paddingTop,10),m=parseInt(f.borderBottomWidth,10),y=p+h+c+parseInt(f.paddingBottom,10)+m,S=Math.min(5*x.offsetHeight,y),C=window.getComputedStyle(w),R=parseInt(C.paddingTop,10),A=parseInt(C.paddingBottom,10),k=e.top+e.height/2-10,T=x.offsetHeight/2,j=p+h+(x.offsetTop+T);if(j<=k){let e=l.length>0&&x===l[l.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-k,T+(e?A:0)+(d.clientHeight-w.offsetTop-w.offsetHeight)+m);u.style.height=j+t+"px"}else{let e=l.length>0&&x===l[0].ref.current;u.style.top="0px";let t=Math.max(k,p+w.offsetTop+(e?R:0)+T);u.style.height=t+(y-j)+"px",w.scrollTop=j-k+w.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=S+"px",u.style.maxHeight=s+"px",null==r||r(),requestAnimationFrame(()=>g.current=!0)}},[v,a.trigger,a.valueNode,u,d,w,x,b,a.dir,r]);(0,eG.N)(()=>C(),[C]);let[R,A]=o.useState();(0,eG.N)(()=>{d&&A(window.getComputedStyle(d).zIndex)},[d]);let k=o.useCallback(e=>{e&&!0===y.current&&(C(),null==S||S(),y.current=!1)},[C,S]);return(0,h.jsx)(tL,{scope:n,contentWrapper:u,shouldExpandOnScrollRef:g,onScrollButtonChange:k,children:(0,h.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,h.jsx)(eW.sG.div,{...l,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tP.displayName="SelectItemAlignedPosition";var tM=o.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=tf(n);return(0,h.jsx)(e2,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tM.displayName="SelectPopperPosition";var[tL,tN]=tc(tR,{}),tD="SelectViewport",tH=o.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...l}=e,i=tT(tD,n),s=tN(tD,n),u=(0,f.s)(t,i.onViewportChange),c=o.useRef(0);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,h.jsx)(ta.Slot,{scope:n,children:(0,h.jsx)(eW.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,a.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if((null==r?void 0:r.current)&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});tH.displayName=tD;var tO="SelectGroup",[tI,tV]=tc(tO);o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,S.B)();return(0,h.jsx)(tI,{scope:n,id:o,children:(0,h.jsx)(eW.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=tO;var tB="SelectLabel";o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tV(tB,n);return(0,h.jsx)(eW.sG.div,{id:o.id,...r,ref:t})}).displayName=tB;var tF="SelectItem",[tW,t_]=tc(tF),tz=o.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:l=!1,textValue:i,...s}=e,u=th(tF,n),c=tT(tF,n),d=u.value===r,[p,m]=o.useState(null!=i?i:""),[v,g]=o.useState(!1),y=(0,f.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,r,l)}),w=(0,S.B)(),x=o.useRef("touch"),b=()=>{l||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,h.jsx)(tW,{scope:n,value:r,disabled:l,textId:w,isSelected:d,onItemTextChange:o.useCallback(e=>{m(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,h.jsx)(ta.ItemSlot,{scope:n,value:r,disabled:l,textValue:p,children:(0,h.jsx)(eW.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":d&&v,"data-state":d?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...s,ref:y,onFocus:(0,a.m)(s.onFocus,()=>g(!0)),onBlur:(0,a.m)(s.onBlur,()=>g(!1)),onClick:(0,a.m)(s.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:(0,a.m)(s.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:(0,a.m)(s.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,a.m)(s.onPointerMove,e=>{if(x.current=e.pointerType,l){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(s.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(s.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(tl.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});tz.displayName=tF;var tG="SelectItemText",tK=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:i,...a}=e,s=th(tG,n),u=tT(tG,n),c=t_(tG,n),d=tv(tG,n),[p,m]=o.useState(null),v=(0,f.s)(t,e=>m(e),c.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,c.value,c.disabled)}),g=null==p?void 0:p.textContent,y=o.useMemo(()=>(0,h.jsx)("option",{value:c.value,disabled:c.disabled,children:g},c.value),[c.disabled,c.value,g]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=d;return(0,eG.N)(()=>(w(y),()=>x(y)),[w,x,y]),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(eW.sG.span,{id:c.textId,...a,ref:v}),c.isSelected&&s.valueNode&&!s.valueNodeHasChildren?l.createPortal(a.children,s.valueNode):null]})});tK.displayName=tG;var tq="SelectItemIndicator",tU=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return t_(tq,n).isSelected?(0,h.jsx)(eW.sG.span,{"aria-hidden":!0,...r,ref:t}):null});tU.displayName=tq;var tY="SelectScrollUpButton",tX=o.forwardRef((e,t)=>{let n=tT(tY,e.__scopeSelect),r=tN(tY,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,f.s)(t,r.onScrollButtonChange);return(0,eG.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,h.jsx)(tJ,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});tX.displayName=tY;var tZ="SelectScrollDownButton",t$=o.forwardRef((e,t)=>{let n=tT(tZ,e.__scopeSelect),r=tN(tZ,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,f.s)(t,r.onScrollButtonChange);return(0,eG.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,h.jsx)(tJ,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});t$.displayName=tZ;var tJ=o.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...l}=e,i=tT("SelectScrollButton",n),s=o.useRef(null),u=ts(n),c=o.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return o.useEffect(()=>()=>c(),[c]),(0,eG.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,h.jsx)(eW.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,a.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(r,50))}),onPointerMove:(0,a.m)(l.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(r,50))}),onPointerLeave:(0,a.m)(l.onPointerLeave,()=>{c()})})});o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,h.jsx)(eW.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var tQ="SelectArrow";o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tf(n),l=th(tQ,n),i=tT(tQ,n);return l.open&&"popper"===i.position?(0,h.jsx)(e6,{...o,...r,ref:t}):null}).displayName=tQ;var t0=o.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...l}=e,i=o.useRef(null),a=(0,f.s)(t,i),s=function(e){let t=o.useRef({value:e,previous:e});return o.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return o.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[s,r]),(0,h.jsx)(eW.sG.select,{...l,style:{...tt,...l.style},ref:a,defaultValue:r})});function t1(e){return""===e||void 0===e}function t2(e){let t=(0,ez.c)(e),n=o.useRef(""),r=o.useRef(0),l=o.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=o.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,l,i]}function t5(e,t,n){var r,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=e,o=Math.max(i,0),r.map((e,t)=>r[(o+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==n?s:void 0}t0.displayName="SelectBubbleInput";var t9=tg,t6=tw,t4=tb,t3=tS,t7=tC,t8=tA,ne=tH,nt=tz,nn=tK,nr=tU,no=tX,nl=t$},91788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])}}]);