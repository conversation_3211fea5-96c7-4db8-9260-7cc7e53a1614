(()=>{var e={};e.id=562,e.ids=[562],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6211:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,BF:()=>l,Hj:()=>o,XI:()=>i,nA:()=>c,nd:()=>d});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function n({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11273:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,q:()=>i});var r=s(43210),a=s(60687);function i(e,t){let s=r.createContext(t),i=e=>{let{children:t,...i}=e,n=r.useMemo(()=>i,Object.values(i));return(0,a.jsx)(s.Provider,{value:n,children:t})};return i.displayName=e+"Provider",[i,function(a){let i=r.useContext(s);if(i)return i;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function n(e,t=[]){let s=[],i=()=>{let t=s.map(e=>r.createContext(e));return function(s){let a=s?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return i.scopeName=e,[function(t,i){let n=r.createContext(i),l=s.length;s=[...s,i];let o=t=>{let{scope:s,children:i,...o}=t,d=s?.[e]?.[l]||n,c=r.useMemo(()=>o,Object.values(o));return(0,a.jsx)(d.Provider,{value:c,children:i})};return o.displayName=t+"Provider",[o,function(s,a){let o=a?.[e]?.[l]||n,d=r.useContext(o);if(d)return d;if(void 0!==i)return i;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=s.reduce((t,{useScope:s,scopeName:r})=>{let a=s(e)[`__scope${r}`];return{...t,...a}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return s.scopeName=t.scopeName,s}(i,...t)]}},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14163:(e,t,s)=>{"use strict";s.d(t,{hO:()=>o,sG:()=>l});var r=s(43210),a=s(51215),i=s(8730),n=s(60687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,i.TL)(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?s:t,{...i,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function o(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},16023:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28227:(e,t,s)=>{Promise.resolve().then(s.bind(s,79340))},28354:e=>{"use strict";e.exports=require("util")},28907:(e,t,s)=>{Promise.resolve().then(s.bind(s,71902))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},46657:(e,t,s)=>{"use strict";s.d(t,{k:()=>y});var r=s(60687),a=s(43210),i=s(11273),n=s(14163),l="Progress",[o,d]=(0,i.A)(l),[c,u]=o(l),x=a.forwardRef((e,t)=>{var s,a;let{__scopeProgress:i,value:l=null,max:o,getValueLabel:d=h,...u}=e;(o||0===o)&&!f(o)&&console.error((s=`${o}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let x=f(o)?o:100;null===l||b(l,x)||console.error((a=`${l}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let p=b(l,x)?l:null,m=v(p)?d(p,x):void 0;return(0,r.jsx)(c,{scope:i,value:p,max:x,children:(0,r.jsx)(n.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":v(p)?p:void 0,"aria-valuetext":m,role:"progressbar","data-state":j(p,x),"data-value":p??void 0,"data-max":x,...u,ref:t})})});x.displayName=l;var p="ProgressIndicator",m=a.forwardRef((e,t)=>{let{__scopeProgress:s,...a}=e,i=u(p,s);return(0,r.jsx)(n.sG.div,{"data-state":j(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...a,ref:t})});function h(e,t){return`${Math.round(e/t*100)}%`}function j(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function v(e){return"number"==typeof e}function f(e){return v(e)&&!isNaN(e)&&e>0}function b(e,t){return v(e)&&!isNaN(e)&&e<=t&&e>=0}m.displayName=p;var g=s(4780);function y({className:e,value:t,...s}){return(0,r.jsx)(x,{"data-slot":"progress",className:(0,g.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,r.jsx)(m,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71902:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\parco-cavi\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79340:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var r=s(60687),a=s(43210),i=s(44493),n=s(29523),l=s(96834),o=s(89667),d=s(6211),c=s(46657),u=s(63213);s(62185);var x=s(19080),p=s(31158),m=s(16023),h=s(96474),j=s(5336),v=s(48730),f=s(93613),b=s(99270),g=s(41862),y=s(13861),N=s(63143),w=s(88233);function A(){let[e,t]=(0,a.useState)(""),[s,A]=(0,a.useState)("all"),[_,k]=(0,a.useState)([]),[C,M]=(0,a.useState)(!0),[P,q]=(0,a.useState)(""),{user:z,cantiere:$}=(0,u.A)(),E=(e,t,s)=>{let a=s>0?t/s*100:0;return 0===a?(0,r.jsx)(l.E,{className:"bg-red-100 text-red-800",children:"Esaurita"}):a<20?(0,r.jsx)(l.E,{className:"bg-orange-100 text-orange-800",children:"Quasi Esaurita"}):a<50?(0,r.jsx)(l.E,{className:"bg-yellow-100 text-yellow-800",children:"In Uso"}):(0,r.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Disponibile"})},S=_.filter(t=>{let r=t.id_bobina?.toLowerCase().includes(e.toLowerCase())||t.numero_bobina?.toLowerCase().includes(e.toLowerCase())||t.tipologia?.toLowerCase().includes(e.toLowerCase())||t.utility?.toLowerCase().includes(e.toLowerCase()),a=!0;if("all"!==s){let e=t.metri_totali>0?t.metri_residui/t.metri_totali*100:0;switch(s){case"disponibile":a=e>=50;break;case"in_uso":a=e>0&&e<50;break;case"esaurita":a=0===e}}return r&&a}),I={totali:_.length,disponibili:_.filter(e=>e.metri_totali>0&&e.metri_residui/e.metri_totali>=.5).length,in_uso:_.filter(e=>{let t=e.metri_totali>0?e.metri_residui/e.metri_totali:0;return t>0&&t<.5}).length,esaurite:_.filter(e=>0===e.metri_residui).length};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600"}),"Parco Cavi"]}),(0,r.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione completa delle bobine e materiali"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Esporta"]}),(0,r.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Importa"]}),(0,r.jsxs)(n.$,{size:"sm",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Nuova Bobina"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:I.totali})]}),(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"Disponibili"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:I.disponibili})]}),(0,r.jsx)(j.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"In Uso"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:I.in_uso})]}),(0,r.jsx)(v.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"Esaurite"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-red-600",children:I.esaurite})]}),(0,r.jsx)(f.A,{className:"h-8 w-8 text-red-500"})]})})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(b.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(o.p,{placeholder:"Cerca per ID bobina, numero, tipologia o utility...",value:e,onChange:e=>t(e.target.value),className:"w-full"})}),(0,r.jsx)("div",{className:"flex gap-2",children:["all","disponibile","in_uso","esaurita"].map(e=>(0,r.jsx)(n.$,{variant:s===e?"default":"outline",size:"sm",onClick:()=>A(e),children:"all"===e?"Tutte":"disponibile"===e?"Disponibili":"in_uso"===e?"In Uso":"Esaurite"},e))})]})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsxs)(i.ZB,{children:["Elenco Bobine (",S.length,")"]}),(0,r.jsx)(i.BT,{children:"Gestione completa delle bobine con stato utilizzo e metrature"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(d.XI,{children:[(0,r.jsx)(d.A0,{children:(0,r.jsxs)(d.Hj,{children:[(0,r.jsx)(d.nd,{children:"ID Bobina"}),(0,r.jsx)(d.nd,{children:"Numero"}),(0,r.jsx)(d.nd,{children:"Utility"}),(0,r.jsx)(d.nd,{children:"Tipologia"}),(0,r.jsx)(d.nd,{children:"Conduttori/Sezione"}),(0,r.jsx)(d.nd,{children:"Metrature"}),(0,r.jsx)(d.nd,{children:"Utilizzo"}),(0,r.jsx)(d.nd,{children:"Stato"}),(0,r.jsx)(d.nd,{children:"Ubicazione"}),(0,r.jsx)(d.nd,{children:"Azioni"})]})}),(0,r.jsx)(d.BF,{children:C?(0,r.jsx)(d.Hj,{children:(0,r.jsx)(d.nA,{colSpan:10,className:"text-center py-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 animate-spin"}),"Caricamento bobine..."]})})}):P?(0,r.jsx)(d.Hj,{children:(0,r.jsx)(d.nA,{colSpan:10,className:"text-center py-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),P]})})}):0===S.length?(0,r.jsx)(d.Hj,{children:(0,r.jsx)(d.nA,{colSpan:10,className:"text-center py-8 text-slate-500",children:"Nessuna bobina trovata"})}):S.map(e=>{let t=e.metri_totali>0?(e.metri_totali-e.metri_residui)/e.metri_totali*100:0;return(0,r.jsxs)(d.Hj,{children:[(0,r.jsx)(d.nA,{className:"font-medium",children:e.id_bobina}),(0,r.jsx)(d.nA,{children:e.numero_bobina||"-"}),(0,r.jsx)(d.nA,{children:e.utility||"-"}),(0,r.jsx)(d.nA,{children:e.tipologia||"-"}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{children:e.n_conduttori||"-"}),(0,r.jsx)("div",{className:"text-slate-500",children:e.sezione||"-"})]})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("div",{children:["Residui: ",(0,r.jsxs)("span",{className:"font-medium",children:[e.metri_residui,"m"]})]}),(0,r.jsxs)("div",{className:"text-slate-500",children:["Totali: ",e.metri_totali,"m"]})]})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-sm font-medium",children:[Math.round(t),"%"]}),(0,r.jsx)(c.k,{value:t,className:"h-2"})]})}),(0,r.jsx)(d.nA,{children:E(e.stato_bobina,e.metri_residui,e.metri_totali)}),(0,r.jsx)(d.nA,{children:(0,r.jsx)(l.E,{variant:"outline",children:e.ubicazione_bobina||"Non specificata"})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(y.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(N.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})})]},e.id_bobina)})})]})})})]})]})})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85902:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["parco-cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,71902)),"C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/parco-cavi/page",pathname:"/parco-cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(60687);s(43210);var a=s(4780);function i({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,341,658,692],()=>s(85902));module.exports=r})();