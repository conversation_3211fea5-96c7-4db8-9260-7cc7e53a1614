exports.id=692,exports.ids=[692],exports.modules={4780:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var r=a(49384),i=a(82348);function s(...e){return(0,i.QP)((0,r.$)(e))}},9107:(e,t,a)=>{Promise.resolve().then(a.bind(a,93319)),Promise.resolve().then(a.bind(a,29131))},10501:(e,t,a)=>{"use strict";a.d(t,{Navbar:()=>A});var r=a(60687),i=a(43210),s=a(85814),n=a.n(s),o=a(16189),c=a(29523),l=a(96834),d=a(63213),m=a(32192),u=a(84027),h=a(17313),p=a(23361),v=a(19080),g=a(6727),x=a(10022),b=a(53411),f=a(58559),w=a(41312),j=a(40083),C=a(11860),y=a(12941);let $=e=>{let t=[{name:"Dashboard",href:"/",icon:m.A}];return"owner"===e?[...t,{name:"Amministrazione",href:"/admin",icon:u.A},{name:"Cantieri",href:"/cantieri",icon:h.A}]:"user"===e?[...t,{name:"Cantieri",href:"/cantieri",icon:h.A}]:"cantieri_user"===e?[{name:"Gestione Cavi",href:"/cavi",icon:p.A},{name:"Parco Cavi",href:"/parco-cavi",icon:v.A},{name:"Comande",href:"/comande",icon:g.A},{name:"Certificazioni",href:"/certificazioni",icon:x.A},{name:"Report",href:"/reports",icon:b.A},{name:"Produttivit\xe0",href:"/productivity",icon:f.A}]:[...t,{name:"Gestione Cavi",href:"/cavi",icon:p.A},{name:"Parco Cavi",href:"/parco-cavi",icon:v.A},{name:"Comande",href:"/comande",icon:g.A},{name:"Certificazioni",href:"/certificazioni",icon:x.A},{name:"Report",href:"/reports",icon:b.A},{name:"Produttivit\xe0",href:"/productivity",icon:f.A}]};function A(){let[e,t]=(0,i.useState)(!1),a=(0,o.usePathname)(),{user:s,cantiere:m,isAuthenticated:u,logout:v}=(0,d.A)(),g=$(s?.ruolo);return"/login"!==a&&u?(0,r.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm",children:[(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(n(),{href:"/",className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{className:"hidden sm:block",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-slate-900",children:"CABLYS"}),(0,r.jsx)("p",{className:"text-xs text-slate-500 -mt-1",children:"Cable Installation System"})]})]})}),(0,r.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:g.map(e=>{let t=a===e.href||"/"!==e.href&&a.startsWith(e.href),i=e.icon;return(0,r.jsx)(n(),{href:e.href,children:(0,r.jsxs)(c.$,{variant:t?"default":"ghost",size:"sm",className:`flex items-center space-x-2 ${t?"bg-blue-600 text-white hover:bg-blue-700":"text-slate-600 hover:text-slate-900 hover:bg-slate-100"}`,children:[(0,r.jsx)(i,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"hidden lg:inline",children:e.name})]})},e.name)})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"hidden sm:flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:s?s.username:m?.commessa}),(0,r.jsx)("p",{className:"text-xs text-slate-500",children:s?s.ruolo:"Cantiere"})]}),(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:s?(0,r.jsx)(w.A,{className:"w-4 h-4 text-white"}):(0,r.jsx)(h.A,{className:"w-4 h-4 text-white"})}),(0,r.jsx)(l.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:"Online"}),(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:v,children:(0,r.jsx)(j.A,{className:"w-4 h-4"})})]}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>t(!e),className:"text-slate-600",children:e?(0,r.jsx)(C.A,{className:"w-5 h-5"}):(0,r.jsx)(y.A,{className:"w-5 h-5"})})})]})]})}),e&&(0,r.jsxs)("div",{className:"md:hidden border-t border-slate-200 bg-white",children:[(0,r.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:g.map(e=>{let i=a===e.href||"/"!==e.href&&a.startsWith(e.href),s=e.icon;return(0,r.jsx)(n(),{href:e.href,children:(0,r.jsxs)(c.$,{variant:i?"default":"ghost",size:"sm",className:`w-full justify-start space-x-3 ${i?"bg-blue-600 text-white":"text-slate-600 hover:text-slate-900 hover:bg-slate-100"}`,onClick:()=>t(!1),children:[(0,r.jsx)(s,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.name})]})},e.name)})}),(0,r.jsx)("div",{className:"border-t border-slate-200 px-4 py-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(w.A,{className:"w-4 h-4 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Admin User"}),(0,r.jsx)("p",{className:"text-xs text-slate-500",children:"Cantiere Demo"})]}),(0,r.jsx)(l.E,{variant:"secondary",className:"bg-green-100 text-green-800 ml-auto",children:"Online"})]})})]})]}):null}},29131:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>i});var r=a(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\contexts\\AuthContext.tsx","useAuth");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\contexts\\AuthContext.tsx","AuthProvider")},29523:(e,t,a)=>{"use strict";a.d(t,{$:()=>c});var r=a(60687);a(43210);var i=a(8730),s=a(24224),n=a(4780);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:t,size:a,asChild:s=!1,...c}){let l=s?i.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:a,className:e})),...c})}},46059:(e,t,a)=>{Promise.resolve().then(a.bind(a,10501)),Promise.resolve().then(a.bind(a,63213))},51021:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},61135:()=>{},62185:(e,t,a)=>{"use strict";a.d(t,{AR:()=>l,At:()=>n,CV:()=>c,FH:()=>i,Fw:()=>o,ZQ:()=>s,_I:()=>m,ug:()=>d});let r=a(51060).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"),Promise.reject(e)));let i={get:async(e,t)=>(await r.get(e,t)).data,post:async(e,t,a)=>(await r.post(e,t,a)).data,put:async(e,t,a)=>(await r.put(e,t,a)).data,patch:async(e,t,a)=>(await r.patch(e,t,a)).data,delete:async(e,t)=>(await r.delete(e,t)).data},s={login:async e=>{let t=new FormData;return t.append("username",e.username),t.append("password",e.password),(await r.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>i.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>i.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},n={getCavi:(e,t)=>i.get(`/api/cavi/${e}`,{params:t}),getCavo:(e,t)=>i.get(`/api/cavi/${e}/${t}`),createCavo:(e,t)=>i.post(`/api/cavi/${e}`,t),updateCavo:(e,t,a)=>i.put(`/api/cavi/${e}/${t}`,a),deleteCavo:(e,t)=>i.delete(`/api/cavi/${e}/${t}`),updateMetriPosati:(e,t,a)=>i.patch(`/api/cavi/${e}/${t}/metri-posati`,{metri_posati:a}),updateBobina:(e,t,a)=>i.patch(`/api/cavi/${e}/${t}/bobina`,{id_bobina:a}),updateCollegamento:(e,t,a)=>i.patch(`/api/cavi/${e}/${t}/collegamento`,{collegamenti:a})},o={getBobine:e=>i.get(`/api/parco-cavi/${e}`),getBobina:(e,t)=>i.get(`/api/parco-cavi/${e}/${t}`),createBobina:(e,t)=>i.post(`/api/parco-cavi/${e}`,t),updateBobina:(e,t,a)=>i.put(`/api/parco-cavi/${e}/${t}`,a),deleteBobina:(e,t)=>i.delete(`/api/parco-cavi/${e}/${t}`)},c={getComande:e=>i.get(`/api/comande/${e}`),getComanda:(e,t)=>i.get(`/api/comande/${e}/${t}`),createComanda:(e,t)=>i.post(`/api/comande/${e}`,t),updateComanda:(e,t,a)=>i.put(`/api/comande/${e}/${t}`,a),deleteComanda:(e,t)=>i.delete(`/api/comande/${e}/${t}`),assegnaCavi:(e,t,a)=>i.post(`/api/comande/${e}/${t}/assegna-cavi`,{cavi_ids:a})},l={getResponsabili:e=>i.get(`/api/responsabili/${e}`),createResponsabile:(e,t)=>i.post(`/api/responsabili/${e}`,t),updateResponsabile:(e,t,a)=>i.put(`/api/responsabili/${e}/${t}`,a),deleteResponsabile:(e,t)=>i.delete(`/api/responsabili/${e}/${t}`)},d={getReportAvanzamento:e=>i.get(`/api/reports/${e}/avanzamento`),getReportBOQ:e=>i.get(`/api/reports/${e}/boq`),getReportUtilizzoBobine:e=>i.get(`/api/reports/${e}/utilizzo-bobine`),getReportProgress:e=>i.get(`/api/reports/${e}/progress`)},m={getCantieri:()=>i.get("/api/cantieri"),getCantiere:e=>i.get(`/api/cantieri/${e}`),createCantiere:e=>i.post("/api/cantieri",e),updateCantiere:(e,t)=>i.put(`/api/cantieri/${e}`,t)}},63213:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>c});var r=a(60687),i=a(43210),s=a(62185);let n=(0,i.createContext)(void 0);function o(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c({children:e}){let[t,a]=(0,i.useState)(null),[o,c]=(0,i.useState)(null),[l,d]=(0,i.useState)(!0),m=async()=>{try{if(!localStorage.getItem("access_token"))return void d(!1);let e=await s.ZQ.verifyToken();if("cantieri_user"===e.role){let t={id_cantiere:e.cantiere_id||0,commessa:e.cantiere_name||e.username,codice_univoco:"",id_utente:e.user_id};c(t),a(null)}else{let t={id_utente:e.user_id,username:e.username,ruolo:e.role};a(t),c(null)}}catch(e){console.error("Errore verifica autenticazione:",e),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data")}finally{d(!1)}},u=async(e,t)=>{try{d(!0);let r=await s.ZQ.login({username:e,password:t});localStorage.setItem("access_token",r.access_token),document.cookie=`access_token=${r.access_token}; path=/; max-age=86400`;let i={id_utente:r.user_id,username:r.username,ruolo:r.role};localStorage.setItem("user_data",JSON.stringify(i)),a(i),c(null)}catch(e){throw console.error("Errore login:",e),e}finally{d(!1)}},h=async(e,t)=>{try{d(!0);let r=await s.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});localStorage.setItem("access_token",r.access_token),document.cookie=`access_token=${r.access_token}; path=/; max-age=86400`;let i={id_cantiere:r.cantiere_id,commessa:r.cantiere_name,codice_univoco:e,id_utente:r.user_id};localStorage.setItem("cantiere_data",JSON.stringify(i)),c(i),a(null)}catch(e){throw console.error("Errore login cantiere:",e),e}finally{d(!1)}};return(0,r.jsx)(n.Provider,{value:{user:t,cantiere:o,isAuthenticated:!!t||!!o,isLoading:l,login:u,loginCantiere:h,logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),document.cookie="access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",a(null),c(null),window.location.href="/login"},checkAuth:m},children:e})}},69581:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},93319:(e,t,a)=>{"use strict";a.d(t,{Navbar:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\components\\layout\\Navbar.tsx","Navbar")},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m,metadata:()=>d});var r=a(37413),i=a(22376),s=a.n(i),n=a(68726),o=a.n(n);a(61135);var c=a(93319),l=a(29131);let d={title:"CABLYS - Cable Installation Advance System",description:"Sistema avanzato per la gestione dell'installazione cavi",manifest:"/manifest.json",themeColor:"#2563eb",viewport:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",appleWebApp:{capable:!0,statusBarStyle:"default",title:"CABLYS"}};function m({children:e}){return(0,r.jsx)("html",{lang:"it",children:(0,r.jsx)("body",{className:`${s().variable} ${o().variable} antialiased`,children:(0,r.jsx)(l.AuthProvider,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-slate-50",children:[(0,r.jsx)(c.Navbar,{}),(0,r.jsx)("main",{className:"pt-16",children:e})]})})})})}},96834:(e,t,a)=>{"use strict";a.d(t,{E:()=>c});var r=a(60687);a(43210);var i=a(8730),s=a(24224),n=a(4780);let o=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,asChild:a=!1,...s}){let c=a?i.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...s})}}};