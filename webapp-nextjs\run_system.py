#!/usr/bin/env python
# run_system.py - Sistema di avvio per webapp-nextjs
import subprocess
import sys
import os
import time
import signal
from pathlib import Path

def check_port_available(port):
    """Verifica se una porta è disponibile"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def run_backend():
    """Avvia il server FastAPI (backend)"""
    print("Avvio del backend...")

    # Verifica se la porta 8001 è disponibile
    if not check_port_available(8001):
        print("⚠️  La porta 8001 è già in uso. Il backend potrebbe essere già in esecuzione.")
        print("   Continuando con il frontend...")
        return "already_running"

    # Ottieni il percorso assoluto della directory webapp (backend)
    current_dir = Path(__file__).resolve().parent
    webapp_dir = current_dir.parent / "webapp"
    backend_dir = webapp_dir / "backend"

    # Verifica che la directory esista
    if not backend_dir.exists():
        print(f"Errore: La directory del backend non esiste: {backend_dir}")
        return None

    # Cambia directory alla webapp per le importazioni corrette
    os.chdir(webapp_dir)

    # Comando per avviare il backend dalla directory webapp
    cmd = [sys.executable, "-m", "uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port=8001", "--reload"]
    print(f"Esecuzione comando: {' '.join(cmd)}")
    print(f"Directory di lavoro: {os.getcwd()}")

    try:
        # Avvia il processo senza reindirizzare l'output
        process = subprocess.Popen(cmd)

        # Attendi un po' per assicurarsi che il server si avvii
        time.sleep(3)
        print("Backend avviato con successo!")
        return process
    except Exception as e:
        print(f"Errore durante l'avvio del backend: {e}")
        return None

def run_frontend():
    """Avvia il server Next.js (frontend)"""
    print("Avvio del frontend Next.js...")

    # Ottieni il percorso assoluto della directory webapp-nextjs
    frontend_dir = Path(__file__).resolve().parent

    # Verifica che la directory esista
    if not frontend_dir.exists():
        print(f"Errore: La directory del frontend non esiste: {frontend_dir}")
        return None

    # Cambia directory al frontend
    os.chdir(frontend_dir)

    # Comando per avviare il frontend Next.js in modalità sviluppo
    cmd = ["npm", "run", "dev"]
    print(f"Esecuzione comando: {' '.join(cmd)}")
    print(f"Directory di lavoro: {os.getcwd()}")

    try:
        # Avvia il processo senza reindirizzare l'output
        process = subprocess.Popen(cmd, shell=True)

        # Attendi un po' per assicurarsi che il server si avvii
        time.sleep(5)
        print("Frontend Next.js avviato con successo!")
        return process
    except Exception as e:
        print(f"Errore durante l'avvio del frontend: {e}")
        return None

def check_dependencies():
    """Verifica che le dipendenze necessarie siano installate"""
    print("Verifica delle dipendenze...")

    # Verifica Node.js e npm
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✓ Node.js: {result.stdout.strip()}")
        else:
            print("✗ Node.js non trovato")
            return False
    except FileNotFoundError:
        print("✗ Node.js non trovato")
        return False

    try:
        result = subprocess.run(["npm", "--version"], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✓ npm: {result.stdout.strip()}")
        else:
            print("✗ npm non trovato")
            return False
    except FileNotFoundError:
        print("✗ npm non trovato")
        return False

    # Verifica Python e uvicorn
    try:
        result = subprocess.run([sys.executable, "--version"], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✓ Python: {result.stdout.strip()}")
        else:
            print("✗ Python non trovato")
            return False
    except FileNotFoundError:
        print("✗ Python non trovato")
        return False

    try:
        result = subprocess.run([sys.executable, "-m", "uvicorn", "--version"], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✓ Uvicorn: {result.stdout.strip()}")
        else:
            print("✗ Uvicorn non trovato")
            return False
    except FileNotFoundError:
        print("✗ Uvicorn non trovato")
        return False

    # Verifica che node_modules esista
    frontend_dir = Path(__file__).resolve().parent
    node_modules = frontend_dir / "node_modules"
    if node_modules.exists():
        print("✓ node_modules trovato")
    else:
        print("✗ node_modules non trovato. Eseguire 'npm install'")
        return False

    print("Tutte le dipendenze sono soddisfatte!")
    return True

def main():
    """Funzione principale"""
    print("\n=== Avvio del sistema CABLYS NEXT.JS WEBAPP ===\n")

    # Verifica dipendenze
    if not check_dependencies():
        print("\nErrore: Dipendenze mancanti. Installare le dipendenze necessarie prima di continuare.")
        return

    # Salva la directory corrente
    original_dir = os.getcwd()

    # Avvia il backend
    backend_process = run_backend()
    backend_already_running = backend_process == "already_running"

    if not backend_process and not backend_already_running:
        print("Errore: Impossibile avviare il backend.")
        return

    # Torna alla directory originale
    os.chdir(original_dir)

    # Avvia il frontend
    frontend_process = run_frontend()
    if not frontend_process:
        print("Errore: Impossibile avviare il frontend.")
        # Termina il backend solo se l'abbiamo avviato noi
        if backend_process and backend_process != "already_running":
            backend_process.terminate()
        return

    # Torna alla directory originale
    os.chdir(original_dir)

    print("\n=== Sistema CABLYS NEXT.JS WEBAPP avviato con successo! ===\n")
    print("Backend API: http://localhost:8001")
    print("Frontend Next.js: http://localhost:3000")
    if backend_already_running:
        print("(Il backend era già in esecuzione)")
    print("\nPremi Ctrl+C per terminare i server")

    # Gestione del segnale di interruzione
    def signal_handler(sig, frame):
        print("\nTerminazione dei server in corso...")
        if frontend_process:
            frontend_process.terminate()
        # Termina il backend solo se l'abbiamo avviato noi
        if backend_process and backend_process != "already_running":
            backend_process.terminate()
        print("Server terminati. Arrivederci!")
        sys.exit(0)

    # Registra il gestore del segnale
    signal.signal(signal.SIGINT, signal_handler)

    # Mantiene il programma in esecuzione
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        # Questo blocco non dovrebbe essere mai raggiunto grazie al signal_handler
        pass

if __name__ == "__main__":
    main()
