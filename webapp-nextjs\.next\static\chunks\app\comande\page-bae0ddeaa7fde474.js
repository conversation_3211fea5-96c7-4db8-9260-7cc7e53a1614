(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[484],{9550:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var s=a(95155),i=a(12115),r=a(66695),n=a(30285),c=a(26126),o=a(40283),l=a(25731),d=a(25273),m=a(84616),p=a(19946);let u=(0,p.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var x=a(40646),h=a(14186),g=a(51154);let v=(0,p.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var b=a(92657),f=a(13717);let j=(0,p.A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);var y=a(17580);function N(){let[e,t]=(0,i.useState)("active"),[a,p]=(0,i.useState)([]),[N,w]=(0,i.useState)([]),[_,k]=(0,i.useState)(!0),[A,C]=(0,i.useState)(""),{user:S,cantiere:z}=(0,o.A)();(0,i.useEffect)(()=>{E()},[]);let E=async()=>{try{k(!0),C("");let e=(null==z?void 0:z.id_cantiere)||(null==S?void 0:S.id_utente);if(!e)return void C("Cantiere non selezionato");let[t,a]=await Promise.all([l.CV.getComande(e),l.AR.getResponsabili(e)]);p(t),w(a)}catch(a){var e,t;console.error("Errore caricamento dati:",a),C((null==(t=a.response)||null==(e=t.data)?void 0:e.detail)||"Errore durante il caricamento dei dati")}finally{k(!1)}},I=e=>{switch(e){case"completata":return(0,s.jsx)(c.E,{className:"bg-green-100 text-green-800",children:"Completata"});case"in_corso":return(0,s.jsx)(c.E,{className:"bg-blue-100 text-blue-800",children:"In Corso"});case"pianificata":return(0,s.jsx)(c.E,{className:"bg-yellow-100 text-yellow-800",children:"Pianificata"});case"sospesa":return(0,s.jsx)(c.E,{className:"bg-red-100 text-red-800",children:"Sospesa"});default:return(0,s.jsx)(c.E,{variant:"secondary",children:e})}},R=e=>(0,s.jsx)(c.E,{className:{POSA:"bg-blue-100 text-blue-800",COLLEGAMENTO_PARTENZA:"bg-green-100 text-green-800",COLLEGAMENTO_ARRIVO:"bg-purple-100 text-purple-800",CERTIFICAZIONE:"bg-orange-100 text-orange-800"}[e]||"bg-gray-100 text-gray-800",children:e}),T=a.filter(t=>{switch(e){case"active":return"IN_CORSO"===t.stato||"ASSEGNATA"===t.stato||"CREATA"===t.stato;case"completed":return"COMPLETATA"===t.stato;default:return!0}}),M={totali:a.length,in_corso:a.filter(e=>"IN_CORSO"===e.stato).length,completate:a.filter(e=>"COMPLETATA"===e.stato).length,pianificate:a.filter(e=>"CREATA"===e.stato||"ASSEGNATA"===e.stato).length};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 text-blue-600"}),"Gestione Comande"]}),(0,s.jsx)("p",{className:"text-slate-600 mt-1",children:"Organizzazione e monitoraggio delle attivit\xe0 di cantiere"})]}),(0,s.jsxs)(n.$,{size:"sm",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Nuova Comanda"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsx)(r.Zp,{children:(0,s.jsx)(r.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:M.totali})]}),(0,s.jsx)(d.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,s.jsx)(r.Zp,{children:(0,s.jsx)(r.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"In Corso"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:M.in_corso})]}),(0,s.jsx)(u,{className:"h-8 w-8 text-blue-500"})]})})}),(0,s.jsx)(r.Zp,{children:(0,s.jsx)(r.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Completate"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-600",children:M.completate})]}),(0,s.jsx)(x.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,s.jsx)(r.Zp,{children:(0,s.jsx)(r.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Pianificate"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:M.pianificate})]}),(0,s.jsx)(h.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)(r.Zp,{children:[(0,s.jsx)(r.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(r.ZB,{children:"Elenco Comande"}),(0,s.jsx)("div",{className:"flex gap-2",children:[{key:"active",label:"Attive"},{key:"completed",label:"Completate"},{key:"all",label:"Tutte"}].map(a=>(0,s.jsx)(n.$,{variant:e===a.key?"default":"outline",size:"sm",onClick:()=>t(a.key),children:a.label},a.key))})]})}),(0,s.jsx)(r.Wu,{children:_?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 animate-spin"}),"Caricamento comande..."]})}):A?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 text-red-600",children:[(0,s.jsx)(v,{className:"h-4 w-4"}),A]})}):0===T.length?(0,s.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessuna comanda trovata"}):(0,s.jsx)("div",{className:"space-y-4",children:T.map(e=>(0,s.jsx)(r.Zp,{className:"border-l-4 border-l-blue-500",children:(0,s.jsxs)(r.Wu,{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-slate-900",children:e.codice_comanda}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:e.descrizione||"Nessuna descrizione"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[R(e.tipo_comanda),I(e.stato)]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-3 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-slate-500",children:"Responsabile"}),(0,s.jsx)("p",{className:"font-medium",children:e.responsabile||"Non assegnato"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-slate-500",children:"Team"}),(0,s.jsxs)("p",{className:"font-medium",children:[e.numero_componenti_squadra||0," persone"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-slate-500",children:"Creazione"}),(0,s.jsx)("p",{className:"font-medium",children:new Date(e.data_creazione).toLocaleDateString("it-IT")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-slate-500",children:"Scadenza"}),(0,s.jsx)("p",{className:"font-medium",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"Non definita"})]})]}),e.data_completamento&&(0,s.jsx)("div",{className:"mb-3 p-2 bg-green-50 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-green-700",children:["Completata il ",new Date(e.data_completamento).toLocaleDateString("it-IT")]})}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",children:[(0,s.jsx)(b.A,{className:"h-4 w-4 mr-1"}),"Dettagli"]}),(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-1"}),"Modifica"]}),"IN_CORSO"===e.stato&&(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",children:[(0,s.jsx)(j,{className:"h-4 w-4 mr-1"}),"Sospendi"]}),("CREATA"===e.stato||"ASSEGNATA"===e.stato)&&(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",children:[(0,s.jsx)(u,{className:"h-4 w-4 mr-1"}),"Avvia"]})]})]})},e.codice_comanda))})})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{children:[(0,s.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(y.A,{className:"h-5 w-5"}),"Responsabili"]}),(0,s.jsx)(r.BT,{children:"Gestione responsabili di cantiere"})]}),(0,s.jsx)(r.Wu,{children:_?(0,s.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 animate-spin mr-2"}),"Caricamento..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"space-y-4",children:N.map(e=>{let t=a.filter(t=>t.responsabile===e.nome_responsabile&&("IN_CORSO"===t.stato||"ASSEGNATA"===t.stato)).length;return(0,s.jsxs)("div",{className:"p-3 bg-slate-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900",children:e.nome_responsabile}),(0,s.jsxs)(c.E,{variant:t>0?"default":"secondary",children:[t," attive"]})]}),(0,s.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[e.numero_telefono&&(0,s.jsx)("p",{children:e.numero_telefono}),e.mail&&(0,s.jsx)("p",{children:e.mail})]})]},e.id_responsabile)})}),(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"w-full mt-4",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Nuovo Responsabile"]})]})})]})})]})]})})}},13717:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17580:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},21815:(e,t,a)=>{Promise.resolve().then(a.bind(a,9550))},25273:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},25731:(e,t,a)=>{"use strict";a.d(t,{AR:()=>l,At:()=>n,CV:()=>o,FH:()=>i,Fw:()=>c,ZQ:()=>r,_I:()=>m,ug:()=>d});let s=a(23464).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"),Promise.reject(e)});let i={get:async(e,t)=>(await s.get(e,t)).data,post:async(e,t,a)=>(await s.post(e,t,a)).data,put:async(e,t,a)=>(await s.put(e,t,a)).data,patch:async(e,t,a)=>(await s.patch(e,t,a)).data,delete:async(e,t)=>(await s.delete(e,t)).data},r={login:async e=>{let t=new FormData;return t.append("username",e.username),t.append("password",e.password),(await s.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>i.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>i.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},n={getCavi:(e,t)=>i.get("/api/cavi/".concat(e),{params:t}),getCavo:(e,t)=>i.get("/api/cavi/".concat(e,"/").concat(t)),createCavo:(e,t)=>i.post("/api/cavi/".concat(e),t),updateCavo:(e,t,a)=>i.put("/api/cavi/".concat(e,"/").concat(t),a),deleteCavo:(e,t)=>i.delete("/api/cavi/".concat(e,"/").concat(t)),updateMetriPosati:(e,t,a)=>i.patch("/api/cavi/".concat(e,"/").concat(t,"/metri-posati"),{metri_posati:a}),updateBobina:(e,t,a)=>i.patch("/api/cavi/".concat(e,"/").concat(t,"/bobina"),{id_bobina:a}),updateCollegamento:(e,t,a)=>i.patch("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),{collegamenti:a})},c={getBobine:e=>i.get("/api/parco-cavi/".concat(e)),getBobina:(e,t)=>i.get("/api/parco-cavi/".concat(e,"/").concat(t)),createBobina:(e,t)=>i.post("/api/parco-cavi/".concat(e),t),updateBobina:(e,t,a)=>i.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>i.delete("/api/parco-cavi/".concat(e,"/").concat(t))},o={getComande:e=>i.get("/api/comande/".concat(e)),getComanda:(e,t)=>i.get("/api/comande/".concat(e,"/").concat(t)),createComanda:(e,t)=>i.post("/api/comande/".concat(e),t),updateComanda:(e,t,a)=>i.put("/api/comande/".concat(e,"/").concat(t),a),deleteComanda:(e,t)=>i.delete("/api/comande/".concat(e,"/").concat(t)),assegnaCavi:(e,t,a)=>i.post("/api/comande/".concat(e,"/").concat(t,"/assegna-cavi"),{cavi_ids:a})},l={getResponsabili:e=>i.get("/api/responsabili/".concat(e)),createResponsabile:(e,t)=>i.post("/api/responsabili/".concat(e),t),updateResponsabile:(e,t,a)=>i.put("/api/responsabili/".concat(e,"/").concat(t),a),deleteResponsabile:(e,t)=>i.delete("/api/responsabili/".concat(e,"/").concat(t))},d={getReportAvanzamento:e=>i.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>i.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>i.get("/api/reports/".concat(e,"/utilizzo-bobine")),getReportProgress:e=>i.get("/api/reports/".concat(e,"/progress"))},m={getCantieri:()=>i.get("/api/cantieri"),getCantiere:e=>i.get("/api/cantieri/".concat(e)),createCantiere:e=>i.post("/api/cantieri",e),updateCantiere:(e,t)=>i.put("/api/cantieri/".concat(e),t)}},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var s=a(95155);a(12115);var i=a(99708),r=a(74466),n=a(59434);let c=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:r=!1,...o}=e,l=r?i.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(c({variant:a}),t),...o})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var s=a(95155);a(12115);var i=a(99708),r=a(74466),n=a(59434);let c=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:r,asChild:o=!1,...l}=e,d=o?i.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,n.cn)(c({variant:a,size:r,className:t})),...l})}},40283:(e,t,a)=>{"use strict";a.d(t,{A:()=>c,AuthProvider:()=>o});var s=a(95155),i=a(12115),r=a(25731);let n=(0,i.createContext)(void 0);function c(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function o(e){let{children:t}=e,[a,c]=(0,i.useState)(null),[o,l]=(0,i.useState)(null),[d,m]=(0,i.useState)(!0);(0,i.useEffect)(()=>{p()},[]);let p=async()=>{try{if(!localStorage.getItem("access_token"))return void m(!1);let e=await r.ZQ.verifyToken();if("cantieri_user"===e.role){let t={id_cantiere:e.cantiere_id||0,commessa:e.cantiere_name||e.username,codice_univoco:"",id_utente:e.user_id};l(t),c(null)}else{let t={id_utente:e.user_id,username:e.username,ruolo:e.role};c(t),l(null)}}catch(e){console.error("Errore verifica autenticazione:",e),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data")}finally{m(!1)}},u=async(e,t)=>{try{m(!0);let a=await r.ZQ.login({username:e,password:t});localStorage.setItem("access_token",a.access_token),document.cookie="access_token=".concat(a.access_token,"; path=/; max-age=").concat(86400);let s={id_utente:a.user_id,username:a.username,ruolo:a.role};localStorage.setItem("user_data",JSON.stringify(s)),c(s),l(null)}catch(e){throw console.error("Errore login:",e),e}finally{m(!1)}},x=async(e,t)=>{try{m(!0);let a=await r.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});localStorage.setItem("access_token",a.access_token),document.cookie="access_token=".concat(a.access_token,"; path=/; max-age=").concat(86400);let s={id_cantiere:a.cantiere_id,commessa:a.cantiere_name,codice_univoco:e,id_utente:a.user_id};localStorage.setItem("cantiere_data",JSON.stringify(s)),l(s),c(null)}catch(e){throw console.error("Errore login cantiere:",e),e}finally{m(!1)}};return(0,s.jsx)(n.Provider,{value:{user:a,cantiere:o,isAuthenticated:!!a||!!o,isLoading:d,login:u,loginCantiere:x,logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),document.cookie="access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",c(null),l(null),window.location.href="/login"},checkAuth:p},children:t})}},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var s=a(52596),i=a(39688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,i.QP)((0,s.$)(t))}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>c,Zp:()=>r,aR:()=>n});var s=a(95155);a(12115);var i=a(59434);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...a})}},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92657:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[836,83,441,684,358],()=>t(21815)),_N_E=e.O()}]);