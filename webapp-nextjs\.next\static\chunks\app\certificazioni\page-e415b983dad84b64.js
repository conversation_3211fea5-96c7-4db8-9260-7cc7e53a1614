(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[992],{11337:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(95155),r=t(12115),i=t(66695),l=t(30285),n=t(26126),c=t(62523),o=t(85127),d=t(40283),x=t(25731),h=t(57434),u=t(91788),m=t(29869),j=t(84616),v=t(40646),f=t(85339),p=t(14186),y=t(47924),g=t(51154),N=t(92657),w=t(13717);let A=(0,t(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);function b(){let[e,s]=(0,r.useState)(""),[t,b]=(0,r.useState)("all"),[k,C]=(0,r.useState)([]),[z,_]=(0,r.useState)(!0),[L,M]=(0,r.useState)(""),{user:E,cantiere:H}=(0,d.A)();(0,r.useEffect)(()=>{S()},[]);let S=async()=>{try{_(!0),M("");let e=(null==H?void 0:H.id_cantiere)||(null==E?void 0:E.id_utente);if(!e)return void M("Cantiere non selezionato");let s=await x.FH.get("/certificazioni/".concat(e));C(s)}catch(t){var e,s;console.error("Errore caricamento certificazioni:",t),M((null==(s=t.response)||null==(e=s.data)?void 0:e.detail)||"Errore durante il caricamento delle certificazioni")}finally{_(!1)}},Z=e=>{switch(null==e?void 0:e.toLowerCase()){case"conforme":case"pass":case"ok":return(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800",children:"Conforme"});case"non conforme":case"fail":case"ko":return(0,a.jsx)(n.E,{className:"bg-red-100 text-red-800",children:"Non Conforme"});case"in corso":case"pending":return(0,a.jsx)(n.E,{className:"bg-yellow-100 text-yellow-800",children:"In Corso"});default:return(0,a.jsx)(n.E,{variant:"secondary",children:e||"Da Verificare"})}},I=k.filter(s=>{var a,r,i,l,n,c,o,d,x;let h=(null==(a=s.id_cavo)?void 0:a.toLowerCase().includes(e.toLowerCase()))||(null==(r=s.operatore)?void 0:r.toLowerCase().includes(e.toLowerCase()))||(null==(i=s.strumento_utilizzato)?void 0:i.toLowerCase().includes(e.toLowerCase())),u=!0;if("all"!==t)switch(t){case"conforme":u=(null==(l=s.risultato)?void 0:l.toLowerCase().includes("conforme"))||(null==(n=s.risultato)?void 0:n.toLowerCase())==="pass";break;case"non_conforme":u=(null==(c=s.risultato)?void 0:c.toLowerCase().includes("non conforme"))||(null==(o=s.risultato)?void 0:o.toLowerCase())==="fail";break;case"in_corso":u=(null==(d=s.risultato)?void 0:d.toLowerCase().includes("corso"))||(null==(x=s.risultato)?void 0:x.toLowerCase())==="pending"}return h&&u}),D={totali:k.length,conformi:k.filter(e=>{var s,t;return(null==(s=e.risultato)?void 0:s.toLowerCase().includes("conforme"))||(null==(t=e.risultato)?void 0:t.toLowerCase())==="pass"}).length,non_conformi:k.filter(e=>{var s,t;return(null==(s=e.risultato)?void 0:s.toLowerCase().includes("non conforme"))||(null==(t=e.risultato)?void 0:t.toLowerCase())==="fail"}).length,in_corso:k.filter(e=>{var s,t;return(null==(s=e.risultato)?void 0:s.toLowerCase().includes("corso"))||(null==(t=e.risultato)?void 0:t.toLowerCase())==="pending"}).length};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-blue-600"}),"Certificazioni"]}),(0,a.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione completa delle certificazioni e test dei cavi"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Esporta"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Importa"]}),(0,a.jsxs)(l.$,{size:"sm",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Nuova Certificazione"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:D.totali})]}),(0,a.jsx)(h.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Conformi"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:D.conformi})]}),(0,a.jsx)(v.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Non Conformi"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-600",children:D.non_conformi})]}),(0,a.jsx)(f.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"In Corso"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:D.in_corso})]}),(0,a.jsx)(p.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(c.p,{placeholder:"Cerca per ID cavo, operatore o strumento...",value:e,onChange:e=>s(e.target.value),className:"w-full"})}),(0,a.jsx)("div",{className:"flex gap-2",children:["all","conforme","non_conforme","in_corso"].map(e=>(0,a.jsx)(l.$,{variant:t===e?"default":"outline",size:"sm",onClick:()=>b(e),children:"all"===e?"Tutte":"conforme"===e?"Conformi":"non_conforme"===e?"Non Conformi":"In Corso"},e))})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{children:["Elenco Certificazioni (",I.length,")"]}),(0,a.jsx)(i.BT,{children:"Gestione completa delle certificazioni con risultati e dettagli tecnici"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(o.XI,{children:[(0,a.jsx)(o.A0,{children:(0,a.jsxs)(o.Hj,{children:[(0,a.jsx)(o.nd,{children:"ID Cavo"}),(0,a.jsx)(o.nd,{children:"Data Certificazione"}),(0,a.jsx)(o.nd,{children:"Risultato"}),(0,a.jsx)(o.nd,{children:"Operatore"}),(0,a.jsx)(o.nd,{children:"Strumento"}),(0,a.jsx)(o.nd,{children:"Note"}),(0,a.jsx)(o.nd,{children:"Azioni"})]})}),(0,a.jsx)(o.BF,{children:z?(0,a.jsx)(o.Hj,{children:(0,a.jsx)(o.nA,{colSpan:7,className:"text-center py-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 animate-spin"}),"Caricamento certificazioni..."]})})}):L?(0,a.jsx)(o.Hj,{children:(0,a.jsx)(o.nA,{colSpan:7,className:"text-center py-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),L]})})}):0===I.length?(0,a.jsx)(o.Hj,{children:(0,a.jsx)(o.nA,{colSpan:7,className:"text-center py-8 text-slate-500",children:"Nessuna certificazione trovata"})}):I.map(e=>(0,a.jsxs)(o.Hj,{children:[(0,a.jsx)(o.nA,{className:"font-medium",children:e.id_cavo}),(0,a.jsx)(o.nA,{children:new Date(e.data_certificazione).toLocaleDateString("it-IT")}),(0,a.jsx)(o.nA,{children:Z(e.risultato)}),(0,a.jsx)(o.nA,{children:e.operatore||"-"}),(0,a.jsx)(o.nA,{children:e.strumento_utilizzato||"-"}),(0,a.jsx)(o.nA,{children:(0,a.jsx)("div",{className:"max-w-xs truncate",title:e.note,children:e.note||"-"})}),(0,a.jsx)(o.nA,{children:(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(N.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(w.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(A,{className:"h-4 w-4"})})]})})]},e.id_certificazione))})]})})})]})]})})}},13717:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var a=t(95155);t(12115);var r=t(99708),i=t(74466),l=t(59434);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,asChild:i=!1,...c}=e,o=i?r.DX:"span";return(0,a.jsx)(o,{"data-slot":"badge",className:(0,l.cn)(n({variant:t}),s),...c})}},29869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},40646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47924:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},51154:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},57434:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},62811:(e,s,t)=>{Promise.resolve().then(t.bind(t,11337))},84616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[836,83,645,441,684,358],()=>s(62811)),_N_E=e.O()}]);