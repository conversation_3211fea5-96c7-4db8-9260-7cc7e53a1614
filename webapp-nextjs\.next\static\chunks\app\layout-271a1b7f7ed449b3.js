(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{381:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},25731:(e,a,t)=>{"use strict";t.d(a,{AR:()=>l,At:()=>s,CV:()=>c,FH:()=>i,Fw:()=>o,ZQ:()=>n,_I:()=>u,ug:()=>d});let r=t(23464).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let a=localStorage.getItem("access_token");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>{var a;return(null==(a=e.response)?void 0:a.status)===401&&(localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"),Promise.reject(e)});let i={get:async(e,a)=>(await r.get(e,a)).data,post:async(e,a,t)=>(await r.post(e,a,t)).data,put:async(e,a,t)=>(await r.put(e,a,t)).data,patch:async(e,a,t)=>(await r.patch(e,a,t)).data,delete:async(e,a)=>(await r.delete(e,a)).data},n={login:async e=>{let a=new FormData;return a.append("username",e.username),a.append("password",e.password),(await r.post("/api/auth/login",a,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>i.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>i.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},s={getCavi:(e,a)=>i.get("/api/cavi/".concat(e),{params:a}),getCavo:(e,a)=>i.get("/api/cavi/".concat(e,"/").concat(a)),createCavo:(e,a)=>i.post("/api/cavi/".concat(e),a),updateCavo:(e,a,t)=>i.put("/api/cavi/".concat(e,"/").concat(a),t),deleteCavo:(e,a)=>i.delete("/api/cavi/".concat(e,"/").concat(a)),updateMetriPosati:(e,a,t)=>i.patch("/api/cavi/".concat(e,"/").concat(a,"/metri-posati"),{metri_posati:t}),updateBobina:(e,a,t)=>i.patch("/api/cavi/".concat(e,"/").concat(a,"/bobina"),{id_bobina:t}),updateCollegamento:(e,a,t)=>i.patch("/api/cavi/".concat(e,"/").concat(a,"/collegamento"),{collegamenti:t})},o={getBobine:e=>i.get("/api/parco-cavi/".concat(e)),getBobina:(e,a)=>i.get("/api/parco-cavi/".concat(e,"/").concat(a)),createBobina:(e,a)=>i.post("/api/parco-cavi/".concat(e),a),updateBobina:(e,a,t)=>i.put("/api/parco-cavi/".concat(e,"/").concat(a),t),deleteBobina:(e,a)=>i.delete("/api/parco-cavi/".concat(e,"/").concat(a))},c={getComande:e=>i.get("/api/comande/".concat(e)),getComanda:(e,a)=>i.get("/api/comande/".concat(e,"/").concat(a)),createComanda:(e,a)=>i.post("/api/comande/".concat(e),a),updateComanda:(e,a,t)=>i.put("/api/comande/".concat(e,"/").concat(a),t),deleteComanda:(e,a)=>i.delete("/api/comande/".concat(e,"/").concat(a)),assegnaCavi:(e,a,t)=>i.post("/api/comande/".concat(e,"/").concat(a,"/assegna-cavi"),{cavi_ids:t})},l={getResponsabili:e=>i.get("/api/responsabili/".concat(e)),createResponsabile:(e,a)=>i.post("/api/responsabili/".concat(e),a),updateResponsabile:(e,a,t)=>i.put("/api/responsabili/".concat(e,"/").concat(a),t),deleteResponsabile:(e,a)=>i.delete("/api/responsabili/".concat(e,"/").concat(a))},d={getReportAvanzamento:e=>i.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>i.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>i.get("/api/reports/".concat(e,"/utilizzo-bobine")),getReportProgress:e=>i.get("/api/reports/".concat(e,"/progress"))},u={getCantieri:()=>i.get("/api/cantieri"),getCantiere:e=>i.get("/api/cantieri/".concat(e)),createCantiere:e=>i.post("/api/cantieri",e),updateCantiere:(e,a)=>i.put("/api/cantieri/".concat(e),a)}},26126:(e,a,t)=>{"use strict";t.d(a,{E:()=>c});var r=t(95155);t(12115);var i=t(99708),n=t(74466),s=t(59434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:a,variant:t,asChild:n=!1,...c}=e,l=n?i.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,s.cn)(o({variant:t}),a),...c})}},27735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>c});var r=t(95155);t(12115);var i=t(99708),n=t(74466),s=t(59434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:a,variant:t,size:n,asChild:c=!1,...l}=e,d=c?i.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,s.cn)(o({variant:t,size:n,className:a})),...l})}},30347:()=>{},37108:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},40283:(e,a,t)=>{"use strict";t.d(a,{A:()=>o,AuthProvider:()=>c});var r=t(95155),i=t(12115),n=t(25731);let s=(0,i.createContext)(void 0);function o(){let e=(0,i.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c(e){let{children:a}=e,[t,o]=(0,i.useState)(null),[c,l]=(0,i.useState)(null),[d,u]=(0,i.useState)(!0);(0,i.useEffect)(()=>{m()},[]);let m=async()=>{try{if(!localStorage.getItem("access_token"))return void u(!1);let e=await n.ZQ.verifyToken();if("cantieri_user"===e.role){let a={id_cantiere:e.cantiere_id||0,commessa:e.cantiere_name||e.username,codice_univoco:"",id_utente:e.user_id};l(a),o(null)}else{let a={id_utente:e.user_id,username:e.username,ruolo:e.role};o(a),l(null)}}catch(e){console.error("Errore verifica autenticazione:",e),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data")}finally{u(!1)}},h=async(e,a)=>{try{u(!0);let t=await n.ZQ.login({username:e,password:a});localStorage.setItem("access_token",t.access_token),document.cookie="access_token=".concat(t.access_token,"; path=/; max-age=").concat(86400);let r={id_utente:t.user_id,username:t.username,ruolo:t.role};localStorage.setItem("user_data",JSON.stringify(r)),o(r),l(null)}catch(e){throw console.error("Errore login:",e),e}finally{u(!1)}},p=async(e,a)=>{try{u(!0);let t=await n.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:a});localStorage.setItem("access_token",t.access_token),document.cookie="access_token=".concat(t.access_token,"; path=/; max-age=").concat(86400);let r={id_cantiere:t.cantiere_id,commessa:t.cantiere_name,codice_univoco:e,id_utente:t.user_id};localStorage.setItem("cantiere_data",JSON.stringify(r)),l(r),o(null)}catch(e){throw console.error("Errore login cantiere:",e),e}finally{u(!1)}};return(0,r.jsx)(s.Provider,{value:{user:t,cantiere:c,isAuthenticated:!!t||!!c,isLoading:d,login:h,loginCantiere:p,logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),document.cookie="access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",o(null),l(null),window.location.href="/login"},checkAuth:m},children:a})}},48949:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,62093,23)),Promise.resolve().then(t.t.bind(t,27735,23)),Promise.resolve().then(t.t.bind(t,30347,23)),Promise.resolve().then(t.bind(t,97680)),Promise.resolve().then(t.bind(t,40283))},54416:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},57434:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},59434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>n});var r=t(52596),i=t(39688);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,i.QP)((0,r.$)(a))}},62093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},97680:(e,a,t)=>{"use strict";t.d(a,{Navbar:()=>A});var r=t(95155),i=t(12115),n=t(6874),s=t.n(n),o=t(35695),c=t(30285),l=t(26126),d=t(40283),u=t(19946);let m=(0,u.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var h=t(381),p=t(23227),v=t(3493),g=t(37108),x=t(25273),f=t(57434),b=t(72713),y=t(79397),w=t(17580);let k=(0,u.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);var _=t(54416);let j=(0,u.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),N=e=>{let a=[{name:"Dashboard",href:"/",icon:m}];return"owner"===e?[...a,{name:"Amministrazione",href:"/admin",icon:h.A},{name:"Cantieri",href:"/cantieri",icon:p.A}]:"user"===e?[...a,{name:"Cantieri",href:"/cantieri",icon:p.A}]:"cantieri_user"===e?[{name:"Gestione Cavi",href:"/cavi",icon:v.A},{name:"Parco Cavi",href:"/parco-cavi",icon:g.A},{name:"Comande",href:"/comande",icon:x.A},{name:"Certificazioni",href:"/certificazioni",icon:f.A},{name:"Report",href:"/reports",icon:b.A},{name:"Produttivit\xe0",href:"/productivity",icon:y.A}]:[...a,{name:"Gestione Cavi",href:"/cavi",icon:v.A},{name:"Parco Cavi",href:"/parco-cavi",icon:g.A},{name:"Comande",href:"/comande",icon:x.A},{name:"Certificazioni",href:"/certificazioni",icon:f.A},{name:"Report",href:"/reports",icon:b.A},{name:"Produttivit\xe0",href:"/productivity",icon:y.A}]};function A(){let[e,a]=(0,i.useState)(!1),t=(0,o.usePathname)(),{user:n,cantiere:u,isAuthenticated:m,logout:h}=(0,d.A)(),g=N(null==n?void 0:n.ruolo);return"/login"!==t&&m?(0,r.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm",children:[(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(s(),{href:"/",className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center",children:(0,r.jsx)(v.A,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{className:"hidden sm:block",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-slate-900",children:"CABLYS"}),(0,r.jsx)("p",{className:"text-xs text-slate-500 -mt-1",children:"Cable Installation System"})]})]})}),(0,r.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:g.map(e=>{let a=t===e.href||"/"!==e.href&&t.startsWith(e.href),i=e.icon;return(0,r.jsx)(s(),{href:e.href,children:(0,r.jsxs)(c.$,{variant:a?"default":"ghost",size:"sm",className:"flex items-center space-x-2 ".concat(a?"bg-blue-600 text-white hover:bg-blue-700":"text-slate-600 hover:text-slate-900 hover:bg-slate-100"),children:[(0,r.jsx)(i,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"hidden lg:inline",children:e.name})]})},e.name)})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"hidden sm:flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:n?n.username:null==u?void 0:u.commessa}),(0,r.jsx)("p",{className:"text-xs text-slate-500",children:n?n.ruolo:"Cantiere"})]}),(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:n?(0,r.jsx)(w.A,{className:"w-4 h-4 text-white"}):(0,r.jsx)(p.A,{className:"w-4 h-4 text-white"})}),(0,r.jsx)(l.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:"Online"}),(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:h,children:(0,r.jsx)(k,{className:"w-4 h-4"})})]}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>a(!e),className:"text-slate-600",children:e?(0,r.jsx)(_.A,{className:"w-5 h-5"}):(0,r.jsx)(j,{className:"w-5 h-5"})})})]})]})}),e&&(0,r.jsxs)("div",{className:"md:hidden border-t border-slate-200 bg-white",children:[(0,r.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:g.map(e=>{let i=t===e.href||"/"!==e.href&&t.startsWith(e.href),n=e.icon;return(0,r.jsx)(s(),{href:e.href,children:(0,r.jsxs)(c.$,{variant:i?"default":"ghost",size:"sm",className:"w-full justify-start space-x-3 ".concat(i?"bg-blue-600 text-white":"text-slate-600 hover:text-slate-900 hover:bg-slate-100"),onClick:()=>a(!1),children:[(0,r.jsx)(n,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.name})]})},e.name)})}),(0,r.jsx)("div",{className:"border-t border-slate-200 px-4 py-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(w.A,{className:"w-4 h-4 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Admin User"}),(0,r.jsx)("p",{className:"text-xs text-slate-500",children:"Cantiere Demo"})]}),(0,r.jsx)(l.E,{variant:"secondary",className:"bg-green-100 text-green-800 ml-auto",children:"Online"})]})})]})]}):null}}},e=>{var a=a=>e(e.s=a);e.O(0,[360,836,83,761,441,684,358],()=>a(48949)),_N_E=e.O()}]);