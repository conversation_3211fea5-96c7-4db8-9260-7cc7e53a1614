(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{14186:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},24944:(e,t,a)=>{"use strict";a.d(t,{k:()=>i});var s=a(95155);a(12115);var r=a(55863),n=a(59434);function i(e){let{className:t,value:a,...i}=e;return(0,s.jsx)(r.bL,{"data-slot":"progress",className:(0,n.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...i,children:(0,s.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})}},25731:(e,t,a)=>{"use strict";a.d(t,{AR:()=>l,At:()=>i,CV:()=>c,FH:()=>r,Fw:()=>o,ZQ:()=>n,_I:()=>u,ug:()=>d});let s=a(23464).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"),Promise.reject(e)});let r={get:async(e,t)=>(await s.get(e,t)).data,post:async(e,t,a)=>(await s.post(e,t,a)).data,put:async(e,t,a)=>(await s.put(e,t,a)).data,patch:async(e,t,a)=>(await s.patch(e,t,a)).data,delete:async(e,t)=>(await s.delete(e,t)).data},n={login:async e=>{let t=new FormData;return t.append("username",e.username),t.append("password",e.password),(await s.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>r.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>r.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},i={getCavi:(e,t)=>r.get("/api/cavi/".concat(e),{params:t}),getCavo:(e,t)=>r.get("/api/cavi/".concat(e,"/").concat(t)),createCavo:(e,t)=>r.post("/api/cavi/".concat(e),t),updateCavo:(e,t,a)=>r.put("/api/cavi/".concat(e,"/").concat(t),a),deleteCavo:(e,t)=>r.delete("/api/cavi/".concat(e,"/").concat(t)),updateMetriPosati:(e,t,a)=>r.patch("/api/cavi/".concat(e,"/").concat(t,"/metri-posati"),{metri_posati:a}),updateBobina:(e,t,a)=>r.patch("/api/cavi/".concat(e,"/").concat(t,"/bobina"),{id_bobina:a}),updateCollegamento:(e,t,a)=>r.patch("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),{collegamenti:a})},o={getBobine:e=>r.get("/api/parco-cavi/".concat(e)),getBobina:(e,t)=>r.get("/api/parco-cavi/".concat(e,"/").concat(t)),createBobina:(e,t)=>r.post("/api/parco-cavi/".concat(e),t),updateBobina:(e,t,a)=>r.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>r.delete("/api/parco-cavi/".concat(e,"/").concat(t))},c={getComande:e=>r.get("/api/comande/".concat(e)),getComanda:(e,t)=>r.get("/api/comande/".concat(e,"/").concat(t)),createComanda:(e,t)=>r.post("/api/comande/".concat(e),t),updateComanda:(e,t,a)=>r.put("/api/comande/".concat(e,"/").concat(t),a),deleteComanda:(e,t)=>r.delete("/api/comande/".concat(e,"/").concat(t)),assegnaCavi:(e,t,a)=>r.post("/api/comande/".concat(e,"/").concat(t,"/assegna-cavi"),{cavi_ids:a})},l={getResponsabili:e=>r.get("/api/responsabili/".concat(e)),createResponsabile:(e,t)=>r.post("/api/responsabili/".concat(e),t),updateResponsabile:(e,t,a)=>r.put("/api/responsabili/".concat(e,"/").concat(t),a),deleteResponsabile:(e,t)=>r.delete("/api/responsabili/".concat(e,"/").concat(t))},d={getReportAvanzamento:e=>r.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>r.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>r.get("/api/reports/".concat(e,"/utilizzo-bobine")),getReportProgress:e=>r.get("/api/reports/".concat(e,"/progress"))},u={getCantieri:()=>r.get("/api/cantieri"),getCantiere:e=>r.get("/api/cantieri/".concat(e)),createCantiere:e=>r.post("/api/cantieri",e),updateCantiere:(e,t)=>r.put("/api/cantieri/".concat(e),t)}},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>c});var s=a(95155);a(12115);var r=a(99708),n=a(74466),i=a(59434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:a,asChild:n=!1,...c}=e,l=n?r.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:a}),t),...c})}},33109:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},33792:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var s=a(95155),r=a(12115),n=a(35695),i=a(40283),o=a(66695),c=a(26126),l=a(24944),d=a(6874),u=a.n(d),m=a(3493),p=a(25273),x=a(79397),v=a(72713),g=a(23227),h=a(33109),f=a(40646),b=a(17580),j=a(14186);function N(){let{user:e,cantiere:t,isAuthenticated:a,isLoading:d}=(0,i.A)(),N=(0,n.useRouter)();if((0,r.useEffect)(()=>{!d&&(a?(null==e?void 0:e.ruolo)==="owner"?N.push("/admin"):(null==e?void 0:e.ruolo)==="user"?N.push("/cantieri"):(null==e?void 0:e.ruolo)==="cantieri_user"?N.push("/cavi"):t&&N.push("/cavi"):N.push("/login"))},[a,d,e,t,N]),d)return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(m.A,{className:"w-8 h-8 text-white"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-slate-900 mb-2",children:"CABLYS"}),(0,s.jsx)("p",{className:"text-slate-600",children:"Caricamento in corso..."})]})});if(!a)return null;let w={totalCantieri:5,activeCantieri:3,totalCables:2450,installedCables:1680,completionPercentage:68.6,activeTeams:12,todayInstallations:45,weeklyTarget:300},y=[{title:"Gestione Cavi",description:"Visualizza e gestisci i cavi del cantiere",href:"/cavi",icon:m.A,color:"bg-blue-500"},{title:"Nuova Comanda",description:"Crea una nuova comanda di lavoro",href:"/comande/new",icon:p.A,color:"bg-green-500"},{title:"Produttivit\xe0",description:"Monitora le performance del team",href:"/productivity",icon:x.A,color:"bg-purple-500"},{title:"Report",description:"Genera report di avanzamento",href:"/reports",icon:v.A,color:"bg-orange-500"}];return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,s.jsx)(g.A,{className:"h-8 w-8 text-blue-600"}),"Dashboard CABLYS"]}),(0,s.jsxs)("p",{className:"text-slate-600 mt-1",children:["Benvenuto, ",e?e.username:null==t?void 0:t.commessa]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(c.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:"Sistema Operativo"}),(0,s.jsxs)("div",{className:"text-right text-sm text-slate-600",children:[(0,s.jsx)("p",{children:"Ultimo aggiornamento"}),(0,s.jsxs)("p",{className:"font-medium",children:["Ora: ",new Date().toLocaleTimeString("it-IT")]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)(o.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium text-slate-600",children:"Avanzamento Totale"}),(0,s.jsx)(h.A,{className:"h-4 w-4 text-blue-500"})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[w.completionPercentage,"%"]}),(0,s.jsx)(l.k,{value:w.completionPercentage,className:"mt-2"}),(0,s.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[w.installedCables," di ",w.totalCables," cavi"]})]})]}),(0,s.jsxs)(o.Zp,{className:"border-l-4 border-l-green-500",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium text-slate-600",children:"Cantieri Attivi"}),(0,s.jsx)(g.A,{className:"h-4 w-4 text-green-500"})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:w.activeCantieri}),(0,s.jsxs)("p",{className:"text-xs text-slate-500",children:["di ",w.totalCantieri," totali"]}),(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsx)(f.A,{className:"h-3 w-3 text-green-500 mr-1"}),(0,s.jsx)("span",{className:"text-xs text-green-600",children:"Tutti operativi"})]})]})]}),(0,s.jsxs)(o.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium text-slate-600",children:"Team Attivi"}),(0,s.jsx)(b.A,{className:"h-4 w-4 text-purple-500"})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:w.activeTeams}),(0,s.jsx)("p",{className:"text-xs text-slate-500",children:"squadre operative"}),(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsx)(x.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,s.jsx)("span",{className:"text-xs text-purple-600",children:"In servizio"})]})]})]}),(0,s.jsxs)(o.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium text-slate-600",children:"Installazioni Oggi"}),(0,s.jsx)(j.A,{className:"h-4 w-4 text-orange-500"})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:w.todayInstallations}),(0,s.jsxs)("p",{className:"text-xs text-slate-500",children:["Target: ",w.weeklyTarget,"/settimana"]}),(0,s.jsx)(l.k,{value:w.todayInstallations/w.weeklyTarget*100,className:"mt-2"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-blue-600"}),"Azioni Rapide"]}),(0,s.jsx)(o.BT,{children:"Accesso diretto alle funzioni principali"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsx)("div",{className:"grid grid-cols-2 gap-4",children:y.map((e,t)=>{let a=e.icon;return(0,s.jsx)(u(),{href:e.href,children:(0,s.jsx)(o.Zp,{className:"hover:shadow-md transition-shadow cursor-pointer border-2 hover:border-blue-200",children:(0,s.jsxs)(o.Wu,{className:"p-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 ".concat(e.color," rounded-lg flex items-center justify-center mb-3"),children:(0,s.jsx)(a,{className:"h-5 w-5 text-white"})}),(0,s.jsx)("h4",{className:"font-medium text-slate-900 mb-1",children:e.title}),(0,s.jsx)("p",{className:"text-xs text-slate-600",children:e.description})]})})},t)})})})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(j.A,{className:"h-5 w-5 text-green-600"}),"Attivit\xe0 Recenti"]}),(0,s.jsx)(o.BT,{children:"Ultime operazioni del sistema"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsx)("div",{className:"space-y-3",children:[{time:"10:30",action:"Cavo installato",details:"FG16OM4-24 - Settore A",status:"success"},{time:"10:15",action:"Comanda completata",details:"CMD-2024-001",status:"success"},{time:"09:45",action:"Nuovo cantiere",details:"Cantiere Milano Nord",status:"info"},{time:"09:30",action:"Alert qualit\xe0",details:"Controllo necessario Settore B",status:"warning"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-slate-50 rounded-lg",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full mt-2 flex-shrink-0 ".concat("success"===e.status?"bg-green-500":"warning"===e.status?"bg-orange-500":"bg-blue-500")}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-slate-900",children:e.action}),(0,s.jsx)("span",{className:"text-xs text-slate-500",children:e.time})]}),(0,s.jsx)("p",{className:"text-sm text-slate-600 truncate",children:e.details})]})]},t))})})]})]})]})})}},40283:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>c});var s=a(95155),r=a(12115),n=a(25731);let i=(0,r.createContext)(void 0);function o(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c(e){let{children:t}=e,[a,o]=(0,r.useState)(null),[c,l]=(0,r.useState)(null),[d,u]=(0,r.useState)(!0);(0,r.useEffect)(()=>{m()},[]);let m=async()=>{try{if(!localStorage.getItem("access_token"))return void u(!1);let e=await n.ZQ.verifyToken();if("cantieri_user"===e.role){let t={id_cantiere:e.cantiere_id||0,commessa:e.cantiere_name||e.username,codice_univoco:"",id_utente:e.user_id};l(t),o(null)}else{let t={id_utente:e.user_id,username:e.username,ruolo:e.role};o(t),l(null)}}catch(e){console.error("Errore verifica autenticazione:",e),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data")}finally{u(!1)}},p=async(e,t)=>{try{u(!0);let a=await n.ZQ.login({username:e,password:t});localStorage.setItem("access_token",a.access_token),document.cookie="access_token=".concat(a.access_token,"; path=/; max-age=").concat(86400);let s={id_utente:a.user_id,username:a.username,ruolo:a.role};localStorage.setItem("user_data",JSON.stringify(s)),o(s),l(null)}catch(e){throw console.error("Errore login:",e),e}finally{u(!1)}},x=async(e,t)=>{try{u(!0);let a=await n.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});localStorage.setItem("access_token",a.access_token),document.cookie="access_token=".concat(a.access_token,"; path=/; max-age=").concat(86400);let s={id_cantiere:a.cantiere_id,commessa:a.cantiere_name,codice_univoco:e,id_utente:a.user_id};localStorage.setItem("cantiere_data",JSON.stringify(s)),l(s),o(null)}catch(e){throw console.error("Errore login cantiere:",e),e}finally{u(!1)}};return(0,s.jsx)(i.Provider,{value:{user:a,cantiere:c,isAuthenticated:!!a||!!c,isLoading:d,login:p,loginCantiere:x,logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),document.cookie="access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",o(null),l(null),window.location.href="/login"},checkAuth:m},children:t})}},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},46081:(e,t,a)=>{"use strict";a.d(t,{A:()=>i,q:()=>n});var s=a(12115),r=a(95155);function n(e,t){let a=s.createContext(t),n=e=>{let{children:t,...n}=e,i=s.useMemo(()=>n,Object.values(n));return(0,r.jsx)(a.Provider,{value:i,children:t})};return n.displayName=e+"Provider",[n,function(r){let n=s.useContext(a);if(n)return n;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}function i(e,t=[]){let a=[],n=()=>{let t=a.map(e=>s.createContext(e));return function(a){let r=a?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...a,[e]:r}}),[a,r])}};return n.scopeName=e,[function(t,n){let i=s.createContext(n),o=a.length;a=[...a,n];let c=t=>{let{scope:a,children:n,...c}=t,l=a?.[e]?.[o]||i,d=s.useMemo(()=>c,Object.values(c));return(0,r.jsx)(l.Provider,{value:d,children:n})};return c.displayName=t+"Provider",[c,function(a,r){let c=r?.[e]?.[o]||i,l=s.useContext(c);if(l)return l;if(void 0!==n)return n;throw Error(`\`${a}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let a=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=a.reduce((t,{useScope:a,scopeName:s})=>{let r=a(e)[`__scope${s}`];return{...t,...r}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return a.scopeName=t.scopeName,a}(n,...t)]}},47434:(e,t,a)=>{Promise.resolve().then(a.bind(a,33792))},55863:(e,t,a)=>{"use strict";a.d(t,{C1:()=>N,bL:()=>j});var s=a(12115),r=a(46081),n=a(63655),i=a(95155),o="Progress",[c,l]=(0,r.A)(o),[d,u]=c(o),m=s.forwardRef((e,t)=>{var a,s,r,o;let{__scopeProgress:c,value:l=null,max:u,getValueLabel:m=v,...p}=e;(u||0===u)&&!f(u)&&console.error((a="".concat(u),s="Progress","Invalid prop `max` of value `".concat(a,"` supplied to `").concat(s,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let x=f(u)?u:100;null===l||b(l,x)||console.error((r="".concat(l),o="Progress","Invalid prop `value` of value `".concat(r,"` supplied to `").concat(o,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let j=b(l,x)?l:null,N=h(j)?m(j,x):void 0;return(0,i.jsx)(d,{scope:c,value:j,max:x,children:(0,i.jsx)(n.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":h(j)?j:void 0,"aria-valuetext":N,role:"progressbar","data-state":g(j,x),"data-value":null!=j?j:void 0,"data-max":x,...p,ref:t})})});m.displayName=o;var p="ProgressIndicator",x=s.forwardRef((e,t)=>{var a;let{__scopeProgress:s,...r}=e,o=u(p,s);return(0,i.jsx)(n.sG.div,{"data-state":g(o.value,o.max),"data-value":null!=(a=o.value)?a:void 0,"data-max":o.max,...r,ref:t})});function v(e,t){return"".concat(Math.round(e/t*100),"%")}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function h(e){return"number"==typeof e}function f(e){return h(e)&&!isNaN(e)&&e>0}function b(e,t){return h(e)&&!isNaN(e)&&e<=t&&e>=0}x.displayName=p;var j=m,N=x},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var s=a(52596),r=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}},63655:(e,t,a)=>{"use strict";a.d(t,{hO:()=>c,sG:()=>o});var s=a(12115),r=a(47650),n=a(99708),i=a(95155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=(0,n.TL)(`Primitive.${t}`),r=s.forwardRef((e,s)=>{let{asChild:r,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(r?a:t,{...n,ref:s})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function c(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i});var s=a(95155);a(12115);var r=a(59434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[836,83,761,441,684,358],()=>t(47434)),_N_E=e.O()}]);