(()=>{var e={};e.id=222,e.ids=[222],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,BF:()=>c,Hj:()=>o,XI:()=>i,nA:()=>d,nd:()=>l});var a=s(60687);s(43210);var r=s(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",e),...t})})}function n({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...t})}function c({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function l({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>c,Zp:()=>i,aR:()=>n});var a=s(60687);s(43210);var r=s(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}},47386:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>l});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),c=s(30893),o={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);s.d(t,o);let l={children:["",{children:["cantieri",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90910)),"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/cantieri/page",pathname:"/cantieri",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>m,Es:()=>x,L3:()=>p,c7:()=>u,lG:()=>c,rr:()=>h,zM:()=>o});var a=s(60687);s(43210);var r=s(37908),i=s(11860),n=s(4780);function c({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,a.jsx)(r.l9,{"data-slot":"dialog-trigger",...e})}function l({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...t}){return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function m({className:e,children:t,showCloseButton:s=!0,...c}){return(0,a.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,a.jsx)(d,{}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...c,children:[t,s&&(0,a.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(i.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function x({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function p({className:e,...t}){return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...t})}function h({className:e,...t}){return(0,a.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}},65087:(e,t,s)=>{Promise.resolve().then(s.bind(s,90910))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75863:(e,t,s)=>{Promise.resolve().then(s.bind(s,79072))},79072:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>_});var a=s(60687),r=s(43210),i=s(16189),n=s(44493),c=s(29523),o=s(89667),l=s(80013),d=s(6211),m=s(63503),u=s(63213),x=s(62185),p=s(41862),h=s(96474),g=s(93613),j=s(99270),f=s(17313);let v=(0,s(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var b=s(13861),N=s(63143);function _(){let{user:e,isAuthenticated:t,isLoading:s}=(0,u.A)(),_=(0,i.useRouter)(),[C,w]=(0,r.useState)([]),[z,y]=(0,r.useState)(!0),[A,k]=(0,r.useState)(""),[q,P]=(0,r.useState)(""),[S,E]=(0,r.useState)(!1),[M,F]=(0,r.useState)(!1),[G,J]=(0,r.useState)(null),[L,I]=(0,r.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),$=async()=>{try{y(!0);let e=await x._I.getCantieri();w(e)}catch(e){console.error("Errore nel caricamento cantieri:",e),k("Errore nel caricamento dei cantieri")}finally{y(!1)}},D=async()=>{try{await x._I.createCantiere(L),E(!1),I({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),$()}catch(e){console.error("Errore nella creazione cantiere:",e),k("Errore nella creazione del cantiere")}},R=async()=>{if(G)try{await x._I.updateCantiere(G.id_cantiere,L),F(!1),J(null),$()}catch(e){console.error("Errore nella modifica cantiere:",e),k("Errore nella modifica del cantiere")}},Z=e=>{localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",e.commessa),_.push(`/cantieri/${e.id_cantiere}`)},B=e=>{J(e),I({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),F(!0)},H=e=>{navigator.clipboard.writeText(e)},O=C.filter(e=>e.commessa.toLowerCase().includes(q.toLowerCase())||e.descrizione?.toLowerCase().includes(q.toLowerCase())||e.nome_cliente?.toLowerCase().includes(q.toLowerCase()));return s?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)(p.A,{className:"h-8 w-8 animate-spin"})}):(0,a.jsxs)("div",{className:"container mx-auto p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Gestione Cantieri"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Gestisci tutti i cantieri e accedi alle loro funzionalit\xe0"})]}),(0,a.jsxs)(m.lG,{open:S,onOpenChange:E,children:[(0,a.jsx)(m.zM,{asChild:!0,children:(0,a.jsxs)(c.$,{children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,a.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(m.c7,{children:[(0,a.jsx)(m.L3,{children:"Crea Nuovo Cantiere"}),(0,a.jsx)(m.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(l.J,{htmlFor:"commessa",className:"text-right",children:"Commessa"}),(0,a.jsx)(o.p,{id:"commessa",value:L.commessa,onChange:e=>I({...L,commessa:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(l.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,a.jsx)(o.p,{id:"descrizione",value:L.descrizione,onChange:e=>I({...L,descrizione:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(l.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,a.jsx)(o.p,{id:"nome_cliente",value:L.nome_cliente,onChange:e=>I({...L,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(l.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,a.jsx)(o.p,{id:"password_cantiere",type:"password",value:L.password_cantiere,onChange:e=>I({...L,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,a.jsx)(m.Es,{children:(0,a.jsx)(c.$,{onClick:D,children:"Crea Cantiere"})})]})]})]}),A&&(0,a.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,a.jsx)("span",{className:"text-red-800",children:A})]})}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(j.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(o.p,{placeholder:"Cerca cantieri...",value:q,onChange:e=>P(e.target.value),className:"pl-8"})]})}),z?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(p.A,{className:"h-8 w-8 animate-spin"})}):0===O.length?(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,a.jsx)(f.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Nessun cantiere trovato"}),(0,a.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:q?"Nessun cantiere corrisponde ai criteri di ricerca":"Crea il tuo primo cantiere per iniziare"}),!q&&(0,a.jsxs)(c.$,{onClick:()=>E(!0),children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Crea Primo Cantiere"]})]})}):(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(d.XI,{children:[(0,a.jsx)(d.A0,{children:(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nd,{children:"Commessa"}),(0,a.jsx)(d.nd,{children:"Descrizione"}),(0,a.jsx)(d.nd,{children:"Cliente"}),(0,a.jsx)(d.nd,{children:"Data Creazione"}),(0,a.jsx)(d.nd,{children:"Codice"}),(0,a.jsx)(d.nd,{className:"text-right",children:"Azioni"})]})}),(0,a.jsx)(d.BF,{children:O.map(e=>(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nA,{className:"font-medium",children:e.commessa}),(0,a.jsx)(d.nA,{children:e.descrizione}),(0,a.jsx)(d.nA,{children:e.nome_cliente}),(0,a.jsx)(d.nA,{children:new Date(e.data_creazione).toLocaleDateString()}),(0,a.jsx)(d.nA,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("code",{className:"text-sm bg-muted px-2 py-1 rounded",children:e.codice_univoco}),(0,a.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>H(e.codice_univoco),children:(0,a.jsx)(v,{className:"h-3 w-3"})})]})}),(0,a.jsx)(d.nA,{className:"text-right",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>Z(e),children:[(0,a.jsx)(b.A,{className:"mr-2 h-3 w-3"}),"Gestisci"]}),(0,a.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>B(e),children:(0,a.jsx)(N.A,{className:"h-3 w-3"})})]})})]},e.id_cantiere))})]})}),(0,a.jsx)(m.lG,{open:M,onOpenChange:F,children:(0,a.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(m.c7,{children:[(0,a.jsx)(m.L3,{children:"Modifica Cantiere"}),(0,a.jsx)(m.rr,{children:"Modifica i dettagli del cantiere selezionato"})]}),(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(l.J,{htmlFor:"edit-commessa",className:"text-right",children:"Commessa"}),(0,a.jsx)(o.p,{id:"edit-commessa",value:L.commessa,onChange:e=>I({...L,commessa:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(l.J,{htmlFor:"edit-descrizione",className:"text-right",children:"Descrizione"}),(0,a.jsx)(o.p,{id:"edit-descrizione",value:L.descrizione,onChange:e=>I({...L,descrizione:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(l.J,{htmlFor:"edit-nome_cliente",className:"text-right",children:"Cliente"}),(0,a.jsx)(o.p,{id:"edit-nome_cliente",value:L.nome_cliente,onChange:e=>I({...L,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(l.J,{htmlFor:"edit-indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,a.jsx)(o.p,{id:"edit-indirizzo_cantiere",value:L.indirizzo_cantiere,onChange:e=>I({...L,indirizzo_cantiere:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(l.J,{htmlFor:"edit-citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,a.jsx)(o.p,{id:"edit-citta_cantiere",value:L.citta_cantiere,onChange:e=>I({...L,citta_cantiere:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(l.J,{htmlFor:"edit-nazione_cantiere",className:"text-right",children:"Nazione"}),(0,a.jsx)(o.p,{id:"edit-nazione_cantiere",value:L.nazione_cantiere,onChange:e=>I({...L,nazione_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,a.jsxs)(m.Es,{children:[(0,a.jsx)(c.$,{variant:"outline",onClick:()=>F(!1),children:"Annulla"}),(0,a.jsx)(c.$,{onClick:R,children:"Salva Modifiche"})]})]})})]})}},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(60687);s(43210);var r=s(78148),i=s(4780);function n({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var a=s(60687);s(43210);var r=s(4780);function i({className:e,type:t,...s}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},90910:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,341,658,24,692],()=>s(47386));module.exports=a})();