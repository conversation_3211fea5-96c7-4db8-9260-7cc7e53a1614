(()=>{var e={};e.id=698,e.ids=[698],e.modules={1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,s,t)=>{"use strict";t.d(s,{A0:()=>n,BF:()=>l,Hj:()=>c,XI:()=>i,nA:()=>o,nd:()=>d});var r=t(60687);t(43210);var a=t(4780);function i({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...s})})}function n({className:e,...s}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...s})}function l({className:e,...s}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...s})}function c({className:e,...s}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...s})}function d({className:e,...s}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s})}function o({className:e,...s}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24883:(e,s,t)=>{Promise.resolve().then(t.bind(t,73987))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41862:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var r=t(60687);t(43210);var a=t(4780);function i({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function n({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function l({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...s})}function c({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...s})}function d({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...s})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73987:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(60687),a=t(43210),i=t(44493),n=t(29523),l=t(96834),c=t(89667),d=t(6211),o=t(63213);t(62185);var x=t(62688);let u=(0,x.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var m=t(84027),h=t(96474),p=t(41312),j=t(17313),f=t(99270),g=t(41862),v=t(93613),b=t(13861),N=t(63143);let w=(0,x.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);function y(){let[e,s]=(0,a.useState)("users"),[t,x]=(0,a.useState)(""),[y,A]=(0,a.useState)([]),[C,z]=(0,a.useState)([]),[k,_]=(0,a.useState)(!0),[S,q]=(0,a.useState)(""),{user:E}=(0,o.A)(),P=e=>{switch(e){case"owner":return(0,r.jsx)(l.E,{className:"bg-purple-100 text-purple-800",children:"Owner"});case"user":return(0,r.jsx)(l.E,{className:"bg-blue-100 text-blue-800",children:"User"});case"cantieri_user":return(0,r.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Cantieri User"});default:return(0,r.jsx)(l.E,{variant:"secondary",children:e})}},L=(e,s)=>{if(!e)return(0,r.jsx)(l.E,{className:"bg-red-100 text-red-800",children:"Disabilitato"});if(s){let e=new Date(s),t=new Date;if(e<t)return(0,r.jsx)(l.E,{className:"bg-red-100 text-red-800",children:"Scaduto"});if(e.getTime()-t.getTime()<6048e5)return(0,r.jsx)(l.E,{className:"bg-yellow-100 text-yellow-800",children:"In Scadenza"})}return(0,r.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Attivo"})},M=y.filter(e=>e.username?.toLowerCase().includes(t.toLowerCase())||e.ragione_sociale?.toLowerCase().includes(t.toLowerCase())||e.email?.toLowerCase().includes(t.toLowerCase())),B=C.filter(e=>e.commessa?.toLowerCase().includes(t.toLowerCase())||e.descrizione?.toLowerCase().includes(t.toLowerCase())||e.nome_cliente?.toLowerCase().includes(t.toLowerCase()));return E?.ruolo!=="owner"?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-6",children:(0,r.jsx)(i.Zp,{className:"w-full max-w-md",children:(0,r.jsxs)(i.Wu,{className:"p-6 text-center",children:[(0,r.jsx)(u,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold text-slate-900 mb-2",children:"Accesso Negato"}),(0,r.jsx)("p",{className:"text-slate-600",children:"Non hai i permessi necessari per accedere a questa sezione."})]})})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,r.jsx)(m.A,{className:"h-8 w-8 text-blue-600"}),"Amministrazione"]}),(0,r.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione utenti, cantieri e configurazioni di sistema"})]}),(0,r.jsxs)(n.$,{size:"sm",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"users"===e?"Nuovo Utente":"Nuovo Cantiere"]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(n.$,{variant:"users"===e?"default":"outline",onClick:()=>s("users"),className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),"Utenti"]}),(0,r.jsxs)(n.$,{variant:"cantieri"===e?"default":"outline",onClick:()=>s("cantieri"),className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),"Cantieri"]}),(0,r.jsxs)(n.$,{variant:"settings"===e?"default":"outline",onClick:()=>s("settings"),className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),"Configurazioni"]})]}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 text-slate-500"}),(0,r.jsx)(c.p,{placeholder:`Cerca ${"users"===e?"utenti":"cantieri"}...`,value:t,onChange:e=>x(e.target.value),className:"flex-1"})]})})}),"users"===e&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Gestione Utenti"}),(0,r.jsx)(i.BT,{children:"Amministrazione utenti e permessi di sistema"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(d.XI,{children:[(0,r.jsx)(d.A0,{children:(0,r.jsxs)(d.Hj,{children:[(0,r.jsx)(d.nd,{children:"Username"}),(0,r.jsx)(d.nd,{children:"Ragione Sociale"}),(0,r.jsx)(d.nd,{children:"Email"}),(0,r.jsx)(d.nd,{children:"Ruolo"}),(0,r.jsx)(d.nd,{children:"Stato"}),(0,r.jsx)(d.nd,{children:"Scadenza"}),(0,r.jsx)(d.nd,{children:"Azioni"})]})}),(0,r.jsx)(d.BF,{children:k?(0,r.jsx)(d.Hj,{children:(0,r.jsx)(d.nA,{colSpan:7,className:"text-center py-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 animate-spin"}),"Caricamento utenti..."]})})}):S?(0,r.jsx)(d.Hj,{children:(0,r.jsx)(d.nA,{colSpan:7,className:"text-center py-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),S]})})}):0===M.length?(0,r.jsx)(d.Hj,{children:(0,r.jsx)(d.nA,{colSpan:7,className:"text-center py-8 text-slate-500",children:"Nessun utente trovato"})}):M.map(e=>(0,r.jsxs)(d.Hj,{children:[(0,r.jsx)(d.nA,{className:"font-medium",children:e.username}),(0,r.jsx)(d.nA,{children:e.ragione_sociale||"-"}),(0,r.jsx)(d.nA,{children:e.email||"-"}),(0,r.jsx)(d.nA,{children:P(e.ruolo)}),(0,r.jsx)(d.nA,{children:L(e.abilitato,e.data_scadenza)}),(0,r.jsx)(d.nA,{children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"Nessuna"}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(b.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(N.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(w,{className:"h-4 w-4"})})]})})]},e.id_utente))})]})})})]}),"cantieri"===e&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Gestione Cantieri"}),(0,r.jsx)(i.BT,{children:"Amministrazione cantieri e configurazioni"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(d.XI,{children:[(0,r.jsx)(d.A0,{children:(0,r.jsxs)(d.Hj,{children:[(0,r.jsx)(d.nd,{children:"Commessa"}),(0,r.jsx)(d.nd,{children:"Descrizione"}),(0,r.jsx)(d.nd,{children:"Cliente"}),(0,r.jsx)(d.nd,{children:"Ubicazione"}),(0,r.jsx)(d.nd,{children:"Data Creazione"}),(0,r.jsx)(d.nd,{children:"Azioni"})]})}),(0,r.jsx)(d.BF,{children:k?(0,r.jsx)(d.Hj,{children:(0,r.jsx)(d.nA,{colSpan:6,className:"text-center py-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 animate-spin"}),"Caricamento cantieri..."]})})}):0===B.length?(0,r.jsx)(d.Hj,{children:(0,r.jsx)(d.nA,{colSpan:6,className:"text-center py-8 text-slate-500",children:"Nessun cantiere trovato"})}):B.map(e=>(0,r.jsxs)(d.Hj,{children:[(0,r.jsx)(d.nA,{className:"font-medium",children:e.commessa}),(0,r.jsx)(d.nA,{children:e.descrizione}),(0,r.jsx)(d.nA,{children:e.nome_cliente||"-"}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{children:e.citta_cantiere||"-"}),(0,r.jsx)("div",{className:"text-slate-500",children:e.nazione_cantiere||"-"})]})}),(0,r.jsx)(d.nA,{children:new Date(e.data_creazione).toLocaleDateString("it-IT")}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(b.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(N.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})]})})]},e.id_cantiere))})]})})})]}),"settings"===e&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Configurazioni Sistema"}),(0,r.jsx)(i.BT,{children:"Impostazioni generali del sistema"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Backup Automatico"}),(0,r.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Attivo"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Notifiche Email"}),(0,r.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Attivo"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Log Audit"}),(0,r.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Attivo"})]})]})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Statistiche Sistema"}),(0,r.jsx)(i.BT,{children:"Informazioni generali del sistema"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Utenti Totali"}),(0,r.jsx)("span",{className:"font-medium",children:y.length})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Cantieri Attivi"}),(0,r.jsx)("span",{className:"font-medium",children:C.length})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Versione Sistema"}),(0,r.jsx)("span",{className:"font-medium",children:"2.0.0"})]})]})})]})]})]})})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88091:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var r=t(60687);t(43210);var a=t(4780);function i({className:e,type:s,...t}){return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96504:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,341,658,692],()=>t(96504));module.exports=r})();