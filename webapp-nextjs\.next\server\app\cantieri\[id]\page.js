(()=>{var e={};e.id=628,e.ids=[628],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20518:(e,s,r)=>{Promise.resolve().then(r.bind(r,69274))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40228:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41862:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,s,r)=>{"use strict";r.d(s,{BT:()=>l,Wu:()=>d,ZB:()=>c,Zp:()=>a,aR:()=>n});var t=r(60687);r(43210);var i=r(4780);function a({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function n({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function c({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",e),...s})}function l({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...s})}function d({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",e),...s})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69274:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\[id]\\page.tsx","default")},70440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83548:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var t=r(60687),i=r(43210),a=r(16189),n=r(44493),c=r(29523),l=r(96834),d=r(63213);r(62185);var o=r(41862),x=r(93613),p=r(62688);let u=(0,p.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var m=r(17313),h=r(58869);let j=(0,p.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var v=r(40228),f=r(23361),g=r(19080),N=r(6727),w=r(10022),y=r(53411);function b(){let{user:e,isAuthenticated:s,isLoading:r}=(0,d.A)(),p=(0,a.useRouter)();parseInt((0,a.useParams)().id);let[b,C]=(0,i.useState)(null),[k,A]=(0,i.useState)(!0),[z,_]=(0,i.useState)(""),q=()=>{p.push("/cantieri")};return r||k?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsx)(o.A,{className:"h-8 w-8 animate-spin"})}):z||!b?(0,t.jsxs)("div",{className:"container mx-auto p-6",children:[(0,t.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,t.jsx)("span",{className:"text-red-800",children:z||"Cantiere non trovato"})]})}),(0,t.jsxs)(c.$,{onClick:q,children:[(0,t.jsx)(u,{className:"mr-2 h-4 w-4"}),"Torna alla Lista Cantieri"]})]}):(0,t.jsxs)("div",{className:"container mx-auto p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"flex items-center space-x-2 mb-2",children:(0,t.jsxs)(c.$,{variant:"ghost",onClick:q,children:[(0,t.jsx)(u,{className:"mr-2 h-4 w-4"}),"Torna ai Cantieri"]})}),(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:b.commessa}),(0,t.jsx)("p",{className:"text-muted-foreground",children:b.descrizione})]}),(0,t.jsxs)(l.E,{variant:"secondary",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"ID: ",b.id_cantiere]})]}),(0,t.jsxs)(n.Zp,{className:"mb-6",children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Informazioni Cantiere"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[b.nome_cliente&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Cliente"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:b.nome_cliente})]})]}),b.indirizzo_cantiere&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(j,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Indirizzo"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:[b.indirizzo_cantiere,b.citta_cantiere&&`, ${b.citta_cantiere}`,b.nazione_cantiere&&`, ${b.nazione_cantiere}`]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Data Creazione"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:new Date(b.data_creazione).toLocaleDateString()})]})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,t.jsxs)(n.Zp,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{p.push("/cavi")},children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Gestione Cavi"})]}),(0,t.jsx)(n.BT,{children:"Visualizza, aggiungi, modifica e gestisci tutti i cavi del cantiere"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(c.$,{variant:"outline",className:"w-full",children:"Accedi alla Gestione Cavi"})})]}),(0,t.jsxs)(n.Zp,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{p.push("/parco-cavi")},children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Parco Cavi"})]}),(0,t.jsx)(n.BT,{children:"Gestisci le bobine disponibili e il magazzino cavi"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(c.$,{variant:"outline",className:"w-full",children:"Accedi al Parco Cavi"})})]}),(0,t.jsxs)(n.Zp,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{p.push("/comande")},children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Gestione Comande"})]}),(0,t.jsx)(n.BT,{children:"Crea e gestisci ordini di lavoro per posa e collegamenti"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(c.$,{variant:"outline",className:"w-full",children:"Accedi alle Comande"})})]}),(0,t.jsxs)(n.Zp,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{p.push("/certificazioni")},children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(w.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Certificazioni"})]}),(0,t.jsx)(n.BT,{children:"Gestisci le certificazioni e gli strumenti di misura"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(c.$,{variant:"outline",className:"w-full",children:"Accedi alle Certificazioni"})})]}),(0,t.jsxs)(n.Zp,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{p.push("/reports")},children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Report e Statistiche"})]}),(0,t.jsx)(n.BT,{children:"Visualizza report di avanzamento e statistiche del cantiere"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(c.$,{variant:"outline",className:"w-full",children:"Accedi ai Report"})})]})]})]})}},83997:e=>{"use strict";e.exports=require("tty")},90254:(e,s,r)=>{Promise.resolve().then(r.bind(r,83548))},93613:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},99704:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d});var t=r(65239),i=r(48088),a=r(88170),n=r.n(a),c=r(30893),l={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);r.d(s,l);let d={children:["",{children:["cantieri",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69274)),"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\[id]\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/cantieri/[id]/page",pathname:"/cantieri/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,341,658,692],()=>r(99704));module.exports=t})();