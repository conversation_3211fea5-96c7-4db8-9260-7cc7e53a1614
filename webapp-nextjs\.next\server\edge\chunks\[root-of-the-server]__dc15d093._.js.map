{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport function middleware(request: NextRequest) {\n  const token = request.cookies.get('access_token')?.value ||\n                request.headers.get('authorization')?.replace('Bearer ', '')\n\n  const isLoginPage = request.nextUrl.pathname === '/login'\n\n  // Se è la pagina di login e l'utente è già autenticato, reindirizza alla dashboard\n  if (isLoginPage && token) {\n    return NextResponse.redirect(new URL('/', request.url))\n  }\n\n  // Se non è autenticato e non è nella pagina di login, reindirizza al login\n  if (!token && !isLoginPage) {\n    return NextResponse.redirect(new URL('/login', request.url))\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,SAAS,WAAW,OAAoB;IAC7C,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB,SACrC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;IAEvE,MAAM,cAAc,QAAQ,OAAO,CAAC,QAAQ,KAAK;IAEjD,mFAAmF;IACnF,IAAI,eAAe,OAAO;QACxB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;IACvD;IAEA,2EAA2E;IAC3E,IAAI,CAAC,SAAS,CAAC,aAAa;QAC1B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}