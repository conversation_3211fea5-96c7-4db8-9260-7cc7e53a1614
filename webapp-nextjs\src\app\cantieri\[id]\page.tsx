'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { cantieriApi } from '@/lib/api'
import { Cantiere } from '@/types'
import {
  ArrowLeft,
  Cable,
  Package,
  ClipboardList,
  FileText,
  BarChart3,
  Building2,
  MapPin,
  Calendar,
  User,
  Loader2,
  AlertCircle
} from 'lucide-react'

export default function CantierePage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const cantiereId = parseInt(params.id as string)
  
  const [cantiere, setCantiere] = useState<Cantiere | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated && cantiereId) {
      loadCantiere()
    }
  }, [isAuthenticated, cantiereId])

  const loadCantiere = async () => {
    try {
      setLoading(true)
      const data = await cantieriApi.getCantiere(cantiereId)
      setCantiere(data)
      
      // Salva il cantiere selezionato nel localStorage
      localStorage.setItem('selectedCantiereId', cantiereId.toString())
      localStorage.setItem('selectedCantiereName', data.commessa)
    } catch (error) {
      console.error('Errore nel caricamento cantiere:', error)
      setError('Errore nel caricamento del cantiere')
    } finally {
      setLoading(false)
    }
  }

  const handleBackToCantieri = () => {
    router.push('/cantieri')
  }

  const navigateToGestioneCavi = () => {
    router.push('/cavi')
  }

  const navigateToParcoCavi = () => {
    router.push('/parco-cavi')
  }

  const navigateToComande = () => {
    router.push('/comande')
  }

  const navigateToCertificazioni = () => {
    router.push('/certificazioni')
  }

  const navigateToReports = () => {
    router.push('/reports')
  }

  if (isLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (error || !cantiere) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-4 p-4 border border-red-200 rounded-lg bg-red-50">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
            <span className="text-red-800">{error || 'Cantiere non trovato'}</span>
          </div>
        </div>
        <Button onClick={handleBackToCantieri}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Torna alla Lista Cantieri
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <Button variant="ghost" onClick={handleBackToCantieri}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Torna ai Cantieri
            </Button>
          </div>
          <h1 className="text-3xl font-bold tracking-tight">{cantiere.commessa}</h1>
          <p className="text-muted-foreground">{cantiere.descrizione}</p>
        </div>
        <Badge variant="secondary">
          <Building2 className="mr-2 h-4 w-4" />
          ID: {cantiere.id_cantiere}
        </Badge>
      </div>

      {/* Informazioni cantiere */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Informazioni Cantiere</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {cantiere.nome_cliente && (
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Cliente</p>
                  <p className="text-sm text-muted-foreground">{cantiere.nome_cliente}</p>
                </div>
              </div>
            )}
            {cantiere.indirizzo_cantiere && (
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Indirizzo</p>
                  <p className="text-sm text-muted-foreground">
                    {cantiere.indirizzo_cantiere}
                    {cantiere.citta_cantiere && `, ${cantiere.citta_cantiere}`}
                    {cantiere.nazione_cantiere && `, ${cantiere.nazione_cantiere}`}
                  </p>
                </div>
              </div>
            )}
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Data Creazione</p>
                <p className="text-sm text-muted-foreground">
                  {new Date(cantiere.data_creazione).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Menu di navigazione */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={navigateToGestioneCavi}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Cable className="h-5 w-5" />
              <span>Gestione Cavi</span>
            </CardTitle>
            <CardDescription>
              Visualizza, aggiungi, modifica e gestisci tutti i cavi del cantiere
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full">
              Accedi alla Gestione Cavi
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={navigateToParcoCavi}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Package className="h-5 w-5" />
              <span>Parco Cavi</span>
            </CardTitle>
            <CardDescription>
              Gestisci le bobine disponibili e il magazzino cavi
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full">
              Accedi al Parco Cavi
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={navigateToComande}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <ClipboardList className="h-5 w-5" />
              <span>Gestione Comande</span>
            </CardTitle>
            <CardDescription>
              Crea e gestisci ordini di lavoro per posa e collegamenti
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full">
              Accedi alle Comande
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={navigateToCertificazioni}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Certificazioni</span>
            </CardTitle>
            <CardDescription>
              Gestisci le certificazioni e gli strumenti di misura
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full">
              Accedi alle Certificazioni
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={navigateToReports}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Report e Statistiche</span>
            </CardTitle>
            <CardDescription>
              Visualizza report di avanzamento e statistiche del cantiere
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full">
              Accedi ai Report
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
