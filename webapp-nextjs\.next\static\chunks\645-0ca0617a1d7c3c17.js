"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[645],{25731:(e,t,a)=>{a.d(t,{AR:()=>l,At:()=>c,CV:()=>s,FH:()=>n,Fw:()=>i,ZQ:()=>r,_I:()=>u,ug:()=>d});let o=a(23464).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"),Promise.reject(e)});let n={get:async(e,t)=>(await o.get(e,t)).data,post:async(e,t,a)=>(await o.post(e,t,a)).data,put:async(e,t,a)=>(await o.put(e,t,a)).data,patch:async(e,t,a)=>(await o.patch(e,t,a)).data,delete:async(e,t)=>(await o.delete(e,t)).data},r={login:async e=>{let t=new FormData;return t.append("username",e.username),t.append("password",e.password),(await o.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>n.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>n.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},c={getCavi:(e,t)=>n.get("/api/cavi/".concat(e),{params:t}),getCavo:(e,t)=>n.get("/api/cavi/".concat(e,"/").concat(t)),createCavo:(e,t)=>n.post("/api/cavi/".concat(e),t),updateCavo:(e,t,a)=>n.put("/api/cavi/".concat(e,"/").concat(t),a),deleteCavo:(e,t)=>n.delete("/api/cavi/".concat(e,"/").concat(t)),updateMetriPosati:(e,t,a)=>n.patch("/api/cavi/".concat(e,"/").concat(t,"/metri-posati"),{metri_posati:a}),updateBobina:(e,t,a)=>n.patch("/api/cavi/".concat(e,"/").concat(t,"/bobina"),{id_bobina:a}),updateCollegamento:(e,t,a)=>n.patch("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),{collegamenti:a})},i={getBobine:e=>n.get("/api/parco-cavi/".concat(e)),getBobina:(e,t)=>n.get("/api/parco-cavi/".concat(e,"/").concat(t)),createBobina:(e,t)=>n.post("/api/parco-cavi/".concat(e),t),updateBobina:(e,t,a)=>n.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>n.delete("/api/parco-cavi/".concat(e,"/").concat(t))},s={getComande:e=>n.get("/api/comande/".concat(e)),getComanda:(e,t)=>n.get("/api/comande/".concat(e,"/").concat(t)),createComanda:(e,t)=>n.post("/api/comande/".concat(e),t),updateComanda:(e,t,a)=>n.put("/api/comande/".concat(e,"/").concat(t),a),deleteComanda:(e,t)=>n.delete("/api/comande/".concat(e,"/").concat(t)),assegnaCavi:(e,t,a)=>n.post("/api/comande/".concat(e,"/").concat(t,"/assegna-cavi"),{cavi_ids:a})},l={getResponsabili:e=>n.get("/api/responsabili/".concat(e)),createResponsabile:(e,t)=>n.post("/api/responsabili/".concat(e),t),updateResponsabile:(e,t,a)=>n.put("/api/responsabili/".concat(e,"/").concat(t),a),deleteResponsabile:(e,t)=>n.delete("/api/responsabili/".concat(e,"/").concat(t))},d={getReportAvanzamento:e=>n.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>n.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>n.get("/api/reports/".concat(e,"/utilizzo-bobine")),getReportProgress:e=>n.get("/api/reports/".concat(e,"/progress"))},u={getCantieri:()=>n.get("/api/cantieri"),getCantiere:e=>n.get("/api/cantieri/".concat(e)),createCantiere:e=>n.post("/api/cantieri",e),updateCantiere:(e,t)=>n.put("/api/cantieri/".concat(e),t)}},30285:(e,t,a)=>{a.d(t,{$:()=>s});var o=a(95155);a(12115);var n=a(99708),r=a(74466),c=a(59434);let i=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function s(e){let{className:t,variant:a,size:r,asChild:s=!1,...l}=e,d=s?n.DX:"button";return(0,o.jsx)(d,{"data-slot":"button",className:(0,c.cn)(i({variant:a,size:r,className:t})),...l})}},40283:(e,t,a)=>{a.d(t,{A:()=>i,AuthProvider:()=>s});var o=a(95155),n=a(12115),r=a(25731);let c=(0,n.createContext)(void 0);function i(){let e=(0,n.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function s(e){let{children:t}=e,[a,i]=(0,n.useState)(null),[s,l]=(0,n.useState)(null),[d,u]=(0,n.useState)(!0);(0,n.useEffect)(()=>{p()},[]);let p=async()=>{try{if(!localStorage.getItem("access_token"))return void u(!1);let e=await r.ZQ.verifyToken();if("cantieri_user"===e.role){let t={id_cantiere:e.cantiere_id||0,commessa:e.cantiere_name||e.username,codice_univoco:"",id_utente:e.user_id};l(t),i(null)}else{let t={id_utente:e.user_id,username:e.username,ruolo:e.role};i(t),l(null)}}catch(e){console.error("Errore verifica autenticazione:",e),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data")}finally{u(!1)}},g=async(e,t)=>{try{u(!0);let a=await r.ZQ.login({username:e,password:t});localStorage.setItem("access_token",a.access_token),document.cookie="access_token=".concat(a.access_token,"; path=/; max-age=").concat(86400);let o={id_utente:a.user_id,username:a.username,ruolo:a.role};localStorage.setItem("user_data",JSON.stringify(o)),i(o),l(null)}catch(e){throw console.error("Errore login:",e),e}finally{u(!1)}},m=async(e,t)=>{try{u(!0);let a=await r.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});localStorage.setItem("access_token",a.access_token),document.cookie="access_token=".concat(a.access_token,"; path=/; max-age=").concat(86400);let o={id_cantiere:a.cantiere_id,commessa:a.cantiere_name,codice_univoco:e,id_utente:a.user_id};localStorage.setItem("cantiere_data",JSON.stringify(o)),l(o),i(null)}catch(e){throw console.error("Errore login cantiere:",e),e}finally{u(!1)}};return(0,o.jsx)(c.Provider,{value:{user:a,cantiere:s,isAuthenticated:!!a||!!s,isLoading:d,login:g,loginCantiere:m,logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),document.cookie="access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",i(null),l(null),window.location.href="/login"},checkAuth:p},children:t})}},59434:(e,t,a)=>{a.d(t,{cn:()=>r});var o=a(52596),n=a(39688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,o.$)(t))}},62523:(e,t,a)=>{a.d(t,{p:()=>r});var o=a(95155);a(12115);var n=a(59434);function r(e){let{className:t,type:a,...r}=e;return(0,o.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...r})}},66695:(e,t,a)=>{a.d(t,{BT:()=>s,Wu:()=>l,ZB:()=>i,Zp:()=>r,aR:()=>c});var o=a(95155);a(12115);var n=a(59434);function r(e){let{className:t,...a}=e;return(0,o.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,o.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,o.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...a})}function s(e){let{className:t,...a}=e;return(0,o.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,o.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...a})}},85127:(e,t,a)=>{a.d(t,{A0:()=>c,BF:()=>i,Hj:()=>s,XI:()=>r,nA:()=>d,nd:()=>l});var o=a(95155);a(12115);var n=a(59434);function r(e){let{className:t,...a}=e;return(0,o.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,o.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",t),...a})})}function c(e){let{className:t,...a}=e;return(0,o.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",t),...a})}function i(e){let{className:t,...a}=e;return(0,o.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",t),...a})}function s(e){let{className:t,...a}=e;return(0,o.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function l(e){let{className:t,...a}=e;return(0,o.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function d(e){let{className:t,...a}=e;return(0,o.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}}}]);