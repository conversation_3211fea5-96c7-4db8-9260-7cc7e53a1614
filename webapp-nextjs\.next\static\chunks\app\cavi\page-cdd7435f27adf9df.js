(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[986],{3469:(e,a,t)=>{Promise.resolve().then(t.bind(t,24716))},24716:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>F});var s=t(95155),i=t(12115),n=t(35695),l=t(66695),r=t(30285),o=t(26126),c=t(62523),d=t(85057),x=t(85127),u=t(54165),m=t(85713),h=t(66474),j=t(5196),p=t(47863),g=t(59434);function v(e){let{...a}=e;return(0,s.jsx)(m.bL,{"data-slot":"select",...a})}function f(e){let{...a}=e;return(0,s.jsx)(m.WT,{"data-slot":"select-value",...a})}function b(e){let{className:a,size:t="default",children:i,...n}=e;return(0,s.jsxs)(m.l9,{"data-slot":"select-trigger","data-size":t,className:(0,g.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...n,children:[i,(0,s.jsx)(m.In,{asChild:!0,children:(0,s.jsx)(h.A,{className:"size-4 opacity-50"})})]})}function _(e){let{className:a,children:t,position:i="popper",...n}=e;return(0,s.jsx)(m.ZL,{children:(0,s.jsxs)(m.UC,{"data-slot":"select-content",className:(0,g.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...n,children:[(0,s.jsx)(w,{}),(0,s.jsx)(m.LM,{className:(0,g.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(y,{})]})})}function N(e){let{className:a,children:t,...i}=e;return(0,s.jsxs)(m.q7,{"data-slot":"select-item",className:(0,g.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(m.VF,{children:(0,s.jsx)(j.A,{className:"size-4"})})}),(0,s.jsx)(m.p4,{children:t})]})}function w(e){let{className:a,...t}=e;return(0,s.jsx)(m.PP,{"data-slot":"select-scroll-up-button",className:(0,g.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(p.A,{className:"size-4"})})}function y(e){let{className:a,...t}=e;return(0,s.jsx)(m.wn,{"data-slot":"select-scroll-down-button",className:(0,g.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(h.A,{className:"size-4"})})}var z=t(40283),A=t(25731),C=t(3493),E=t(91788),k=t(29869),I=t(84616),S=t(40646),B=t(14186),L=t(85339),O=t(47924),T=t(51154),V=t(92657),$=t(13717),Z=t(62525);function F(){let[e,a]=(0,i.useState)(""),[t,m]=(0,i.useState)("all"),[h,j]=(0,i.useState)([]),[p,g]=(0,i.useState)(!0),[w,y]=(0,i.useState)(""),[F,P]=(0,i.useState)(null),[U,M]=(0,i.useState)(!1),[W,G]=(0,i.useState)(!1),[H,J]=(0,i.useState)(!1),[D,R]=(0,i.useState)(!1),[X,q]=(0,i.useState)([]),[Y,K]=(0,i.useState)({id_cavo:"",revisione_ufficiale:"",tipologia:"",n_conduttori:"",sezione:"",ubicazione_partenza:"",ubicazione_arrivo:"",metri_teorici:0,responsabile_posa:"",metri_posati:0,id_bobina:""}),{user:Q,cantiere:ee}=(0,z.A)();(0,n.useRouter)();let[ea,et]=(0,i.useState)(0);(0,i.useEffect)(()=>{et((null==ee?void 0:ee.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[ee]),(0,i.useEffect)(()=>{ea&&(es(),ei())},[ea]);let es=async()=>{try{if(g(!0),y(""),!ea)return void y("Cantiere non selezionato");let a=await A.At.getCavi(ea,{search:e,stato_installazione:"all"===t?void 0:t});j(a)}catch(e){var a,s;console.error("Errore caricamento cavi:",e),y((null==(s=e.response)||null==(a=s.data)?void 0:a.detail)||"Errore durante il caricamento dei cavi")}finally{g(!1)}},ei=async()=>{try{let e=await A.Fw.getBobine(ea);q(e)}catch(e){console.error("Errore caricamento bobine:",e)}},en=async()=>{if(F)try{await A.At.updateMetriPosati(ea,F.id_cavo,Y.metri_posati),J(!1),P(null),er(),es()}catch(e){console.error("Errore nell'aggiornamento metri:",e),y("Errore nell'aggiornamento dei metri")}},el=async()=>{if(F)try{await A.At.updateBobina(ea,F.id_cavo,Y.id_bobina),R(!1),P(null),er(),es()}catch(e){console.error("Errore nell'aggiornamento bobina:",e),y("Errore nell'aggiornamento della bobina")}},er=()=>{K({id_cavo:"",revisione_ufficiale:"",tipologia:"",n_conduttori:"",sezione:"",ubicazione_partenza:"",ubicazione_arrivo:"",metri_teorici:0,responsabile_posa:"",metri_posati:0,id_bobina:""})},eo=e=>{P(e),K({id_cavo:e.id_cavo||"",revisione_ufficiale:e.revisione_ufficiale||"",tipologia:e.tipologia||"",n_conduttori:e.n_conduttori||"",sezione:e.sezione||"",ubicazione_partenza:e.ubicazione_partenza||"",ubicazione_arrivo:e.ubicazione_arrivo||"",metri_teorici:e.metri_teorici||0,responsabile_posa:e.responsabile_posa||"",metri_posati:e.metratura_reale||0,id_bobina:e.id_bobina||""}),G(!0)},ec=e=>{P(e),K({...Y,metri_posati:e.metratura_reale||0,id_bobina:e.id_bobina||""}),J(!0)},ed=e=>{P(e),K({...Y,id_bobina:e.id_bobina||""}),R(!0)};(0,i.useEffect)(()=>{let e=setTimeout(()=>{es()},500);return()=>clearTimeout(e)},[e,t]);let ex=e=>{switch(e){case"installato":return(0,s.jsx)(o.E,{className:"bg-green-100 text-green-800",children:"Installato"});case"in_corso":return(0,s.jsx)(o.E,{className:"bg-yellow-100 text-yellow-800",children:"In Corso"});case"non_installato":case"":case null:case void 0:return(0,s.jsx)(o.E,{className:"bg-gray-100 text-gray-800",children:"Non Installato"});default:return(0,s.jsx)(o.E,{variant:"secondary",children:e})}},eu=e=>{switch(e){case 3:return(0,s.jsx)(o.E,{className:"bg-green-100 text-green-800",children:"Collegato"});case 1:case 2:return(0,s.jsx)(o.E,{className:"bg-yellow-100 text-yellow-800",children:"Parziale"});case 0:case null:case void 0:return(0,s.jsx)(o.E,{className:"bg-gray-100 text-gray-800",children:"Non Collegato"});default:return(0,s.jsxs)(o.E,{variant:"secondary",children:["Stato ",e]})}},em=e=>e.data_certificazione?(0,s.jsx)(o.E,{className:"bg-green-100 text-green-800",children:"Certificato"}):e.comanda_certificazione?(0,s.jsx)(o.E,{className:"bg-yellow-100 text-yellow-800",children:"In Corso"}):(0,s.jsx)(o.E,{className:"bg-gray-100 text-gray-800",children:"Non Certificato"}),eh=h.filter(a=>{var s,i,n,l;let r=(null==(s=a.id_cavo)?void 0:s.toLowerCase().includes(e.toLowerCase()))||(null==(i=a.tipologia)?void 0:i.toLowerCase().includes(e.toLowerCase()))||(null==(n=a.n_conduttori)?void 0:n.toLowerCase().includes(e.toLowerCase()))||(null==(l=a.sezione)?void 0:l.toLowerCase().includes(e.toLowerCase())),o="all"===t||a.stato_installazione===t;return r&&o}),ej={totali:h.length,installati:h.filter(e=>e.metratura_reale&&e.metratura_reale>0).length,in_corso:h.filter(e=>e.comanda_posa&&!e.data_posa).length,non_installati:h.filter(e=>!e.metratura_reale||0===e.metratura_reale).length};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,s.jsx)(C.A,{className:"h-8 w-8 text-blue-600"}),"Gestione Cavi"]}),(0,s.jsx)("p",{className:"text-slate-600 mt-1",children:"Visualizzazione e gestione completa dei cavi del cantiere"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(r.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Esporta"]}),(0,s.jsxs)(r.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Importa"]}),(0,s.jsxs)(r.$,{size:"sm",children:[(0,s.jsx)(I.A,{className:"h-4 w-4 mr-2"}),"Nuovo Cavo"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:ej.totali})]}),(0,s.jsx)(C.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Installati"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-600",children:ej.installati})]}),(0,s.jsx)(S.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"In Corso"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:ej.in_corso})]}),(0,s.jsx)(B.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Da Installare"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-600",children:ej.non_installati})]}),(0,s.jsx)(L.A,{className:"h-8 w-8 text-gray-500"})]})})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(O.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)(c.p,{placeholder:"Cerca per nomenclatura, tipologia o formazione...",value:e,onChange:e=>a(e.target.value),className:"w-full"})}),(0,s.jsx)("div",{className:"flex gap-2",children:["all","installato","in_corso","non_installato"].map(e=>(0,s.jsx)(r.$,{variant:t===e?"default":"outline",size:"sm",onClick:()=>m(e),children:"all"===e?"Tutti":"installato"===e?"Installati":"in_corso"===e?"In Corso":"Da Installare"},e))})]})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{children:["Elenco Cavi (",eh.length,")"]}),(0,s.jsx)(l.BT,{children:"Gestione completa dei cavi con stato installazione, collegamento e certificazione"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(x.XI,{children:[(0,s.jsx)(x.A0,{children:(0,s.jsxs)(x.Hj,{children:[(0,s.jsx)(x.nd,{children:"ID Cavo"}),(0,s.jsx)(x.nd,{children:"Tipologia"}),(0,s.jsx)(x.nd,{children:"Conduttori/Sezione"}),(0,s.jsx)(x.nd,{children:"Partenza → Arrivo"}),(0,s.jsx)(x.nd,{children:"Lunghezza"}),(0,s.jsx)(x.nd,{children:"Bobina"}),(0,s.jsx)(x.nd,{children:"Stato"}),(0,s.jsx)(x.nd,{children:"Collegamento"}),(0,s.jsx)(x.nd,{children:"Certificazione"}),(0,s.jsx)(x.nd,{children:"Azioni"})]})}),(0,s.jsx)(x.BF,{children:p?(0,s.jsx)(x.Hj,{children:(0,s.jsx)(x.nA,{colSpan:10,className:"text-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(T.A,{className:"h-4 w-4 animate-spin"}),"Caricamento cavi..."]})})}):w?(0,s.jsx)(x.Hj,{children:(0,s.jsx)(x.nA,{colSpan:10,className:"text-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,s.jsx)(L.A,{className:"h-4 w-4"}),w]})})}):0===eh.length?(0,s.jsx)(x.Hj,{children:(0,s.jsx)(x.nA,{colSpan:10,className:"text-center py-8 text-slate-500",children:"Nessun cavo trovato"})}):eh.map(e=>(0,s.jsxs)(x.Hj,{children:[(0,s.jsx)(x.nA,{className:"font-medium",children:e.id_cavo}),(0,s.jsx)(x.nA,{children:e.tipologia||"-"}),(0,s.jsx)(x.nA,{children:(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("div",{children:e.n_conduttori||"-"}),(0,s.jsx)("div",{className:"text-slate-500",children:e.sezione||"-"})]})}),(0,s.jsx)(x.nA,{children:(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("div",{className:"font-medium",children:e.ubicazione_partenza||"-"}),(0,s.jsx)("div",{className:"text-slate-500",children:"↓"}),(0,s.jsx)("div",{className:"font-medium",children:e.ubicazione_arrivo||"-"})]})}),(0,s.jsx)(x.nA,{children:(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("div",{children:[e.metratura_reale||0,"/",e.metri_teorici||0,"m"]}),(0,s.jsxs)("div",{className:"text-slate-500",children:[e.metri_teorici?Math.round((e.metratura_reale||0)/e.metri_teorici*100):0,"%"]})]})}),(0,s.jsx)(x.nA,{children:(0,s.jsx)(o.E,{variant:"BOBINA_VUOTA"===e.id_bobina?"destructive":"secondary",children:e.id_bobina||"BOBINA_VUOTA"})}),(0,s.jsx)(x.nA,{children:ex(e.stato_installazione)}),(0,s.jsx)(x.nA,{children:eu(e.collegamenti)}),(0,s.jsx)(x.nA,{children:em(e)}),(0,s.jsx)(x.nA,{children:(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>ec(e),disabled:"installato"===e.stato_installazione,children:(0,s.jsx)(V.A,{className:"h-4 w-4"})}),(0,s.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>eo(e),children:(0,s.jsx)($.A,{className:"h-4 w-4"})}),(0,s.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>ed(e),children:(0,s.jsx)(Z.A,{className:"h-4 w-4"})})]})})]},e.id_cavo))})]})})})]}),(0,s.jsx)(u.lG,{open:H,onOpenChange:J,children:(0,s.jsxs)(u.Cf,{children:[(0,s.jsxs)(u.c7,{children:[(0,s.jsx)(u.L3,{children:"Inserisci Metri Posati"}),(0,s.jsxs)(u.rr,{children:["Cavo: ",null==F?void 0:F.id_cavo]})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"metri_posati",children:"Metri Posati"}),(0,s.jsx)(c.p,{id:"metri_posati",type:"number",value:Y.metri_posati,onChange:e=>K({...Y,metri_posati:parseInt(e.target.value)||0})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"bobina_metri",children:"Bobina"}),(0,s.jsxs)(v,{value:Y.id_bobina,onValueChange:e=>K({...Y,id_bobina:e}),children:[(0,s.jsx)(b,{children:(0,s.jsx)(f,{placeholder:"Seleziona bobina"})}),(0,s.jsxs)(_,{children:[(0,s.jsx)(N,{value:"BOBINA_VUOTA",children:"BOBINA_VUOTA"}),X.map(e=>(0,s.jsxs)(N,{value:e.id_bobina,children:[e.numero_bobina," - ",e.tipologia," (",e.metri_residui,"m)"]},e.id_bobina))]})]})]})]}),(0,s.jsxs)(u.Es,{children:[(0,s.jsx)(r.$,{variant:"outline",onClick:()=>J(!1),children:"Annulla"}),(0,s.jsx)(r.$,{onClick:en,children:"Salva"})]})]})}),(0,s.jsx)(u.lG,{open:D,onOpenChange:R,children:(0,s.jsxs)(u.Cf,{children:[(0,s.jsxs)(u.c7,{children:[(0,s.jsx)(u.L3,{children:"Modifica Bobina"}),(0,s.jsxs)(u.rr,{children:["Cavo: ",null==F?void 0:F.id_cavo]})]}),(0,s.jsx)("div",{className:"grid gap-4 py-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"bobina_modifica",children:"Nuova Bobina"}),(0,s.jsxs)(v,{value:Y.id_bobina,onValueChange:e=>K({...Y,id_bobina:e}),children:[(0,s.jsx)(b,{children:(0,s.jsx)(f,{placeholder:"Seleziona bobina"})}),(0,s.jsxs)(_,{children:[(0,s.jsx)(N,{value:"BOBINA_VUOTA",children:"BOBINA_VUOTA"}),X.map(e=>(0,s.jsxs)(N,{value:e.id_bobina,children:[e.numero_bobina," - ",e.tipologia," (",e.metri_residui,"m)"]},e.id_bobina))]})]})]})}),(0,s.jsxs)(u.Es,{children:[(0,s.jsx)(r.$,{variant:"outline",onClick:()=>R(!1),children:"Annulla"}),(0,s.jsx)(r.$,{onClick:el,children:"Salva"})]})]})})]})})}},26126:(e,a,t)=>{"use strict";t.d(a,{E:()=>o});var s=t(95155);t(12115);var i=t(99708),n=t(74466),l=t(59434);let r=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:a,variant:t,asChild:n=!1,...o}=e,c=n?i.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(r({variant:t}),a),...o})}},54165:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>x,Es:()=>m,L3:()=>h,c7:()=>u,lG:()=>r,rr:()=>j,zM:()=>o});var s=t(95155);t(12115);var i=t(50237),n=t(54416),l=t(59434);function r(e){let{...a}=e;return(0,s.jsx)(i.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,s.jsx)(i.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,s.jsx)(i.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...t}=e;return(0,s.jsx)(i.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function x(e){let{className:a,children:t,showCloseButton:r=!0,...o}=e;return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(i.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[t,r&&(0,s.jsxs)(i.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function m(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function h(e){let{className:a,...t}=e;return(0,s.jsx)(i.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",a),...t})}function j(e){let{className:a,...t}=e;return(0,s.jsx)(i.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",a),...t})}},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>l});var s=t(95155);t(12115);var i=t(40968),n=t(59434);function l(e){let{className:a,...t}=e;return(0,s.jsx)(i.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}}},e=>{var a=a=>e(e.s=a);e.O(0,[836,83,464,952,645,441,684,358],()=>a(3469)),_N_E=e.O()}]);