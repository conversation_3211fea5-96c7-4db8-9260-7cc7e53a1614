(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[628],{25273:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(19946).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},37108:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},51018:(e,s,a)=>{Promise.resolve().then(a.bind(a,79553))},57434:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},69074:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},72713:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},79553:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>z});var i=a(95155),t=a(12115),c=a(35695),r=a(66695),l=a(30285),n=a(26126),d=a(40283),h=a(25731),o=a(51154),x=a(85339),m=a(19946);let p=(0,m.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var j=a(23227),u=a(71007);let v=(0,m.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var N=a(69074),f=a(3493),g=a(37108),k=a(25273),y=a(57434),w=a(72713);function z(){let{user:e,isAuthenticated:s,isLoading:a}=(0,d.A)(),m=(0,c.useRouter)(),z=parseInt((0,c.useParams)().id),[A,C]=(0,t.useState)(null),[M,b]=(0,t.useState)(!0),[_,Z]=(0,t.useState)("");(0,t.useEffect)(()=>{a||s||m.push("/login")},[s,a,m]),(0,t.useEffect)(()=>{s&&z&&B()},[s,z]);let B=async()=>{try{b(!0);let e=await h._I.getCantiere(z);C(e),localStorage.setItem("selectedCantiereId",z.toString()),localStorage.setItem("selectedCantiereName",e.commessa)}catch(e){console.error("Errore nel caricamento cantiere:",e),Z("Errore nel caricamento del cantiere")}finally{b(!1)}},R=()=>{m.push("/cantieri")};return a||M?(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsx)(o.A,{className:"h-8 w-8 animate-spin"})}):_||!A?(0,i.jsxs)("div",{className:"container mx-auto p-6",children:[(0,i.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(x.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,i.jsx)("span",{className:"text-red-800",children:_||"Cantiere non trovato"})]})}),(0,i.jsxs)(l.$,{onClick:R,children:[(0,i.jsx)(p,{className:"mr-2 h-4 w-4"}),"Torna alla Lista Cantieri"]})]}):(0,i.jsxs)("div",{className:"container mx-auto p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"flex items-center space-x-2 mb-2",children:(0,i.jsxs)(l.$,{variant:"ghost",onClick:R,children:[(0,i.jsx)(p,{className:"mr-2 h-4 w-4"}),"Torna ai Cantieri"]})}),(0,i.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:A.commessa}),(0,i.jsx)("p",{className:"text-muted-foreground",children:A.descrizione})]}),(0,i.jsxs)(n.E,{variant:"secondary",children:[(0,i.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"ID: ",A.id_cantiere]})]}),(0,i.jsxs)(r.Zp,{className:"mb-6",children:[(0,i.jsx)(r.aR,{children:(0,i.jsx)(r.ZB,{children:"Informazioni Cantiere"})}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[A.nome_cliente&&(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium",children:"Cliente"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:A.nome_cliente})]})]}),A.indirizzo_cantiere&&(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(v,{className:"h-4 w-4 text-muted-foreground"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium",children:"Indirizzo"}),(0,i.jsxs)("p",{className:"text-sm text-muted-foreground",children:[A.indirizzo_cantiere,A.citta_cantiere&&", ".concat(A.citta_cantiere),A.nazione_cantiere&&", ".concat(A.nazione_cantiere)]})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium",children:"Data Creazione"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:new Date(A.data_creazione).toLocaleDateString()})]})]})]})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,i.jsxs)(r.Zp,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{m.push("/cavi")},children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)(f.A,{className:"h-5 w-5"}),(0,i.jsx)("span",{children:"Gestione Cavi"})]}),(0,i.jsx)(r.BT,{children:"Visualizza, aggiungi, modifica e gestisci tutti i cavi del cantiere"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(l.$,{variant:"outline",className:"w-full",children:"Accedi alla Gestione Cavi"})})]}),(0,i.jsxs)(r.Zp,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{m.push("/parco-cavi")},children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)(g.A,{className:"h-5 w-5"}),(0,i.jsx)("span",{children:"Parco Cavi"})]}),(0,i.jsx)(r.BT,{children:"Gestisci le bobine disponibili e il magazzino cavi"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(l.$,{variant:"outline",className:"w-full",children:"Accedi al Parco Cavi"})})]}),(0,i.jsxs)(r.Zp,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{m.push("/comande")},children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)(k.A,{className:"h-5 w-5"}),(0,i.jsx)("span",{children:"Gestione Comande"})]}),(0,i.jsx)(r.BT,{children:"Crea e gestisci ordini di lavoro per posa e collegamenti"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(l.$,{variant:"outline",className:"w-full",children:"Accedi alle Comande"})})]}),(0,i.jsxs)(r.Zp,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{m.push("/certificazioni")},children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)(y.A,{className:"h-5 w-5"}),(0,i.jsx)("span",{children:"Certificazioni"})]}),(0,i.jsx)(r.BT,{children:"Gestisci le certificazioni e gli strumenti di misura"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(l.$,{variant:"outline",className:"w-full",children:"Accedi alle Certificazioni"})})]}),(0,i.jsxs)(r.Zp,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{m.push("/reports")},children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)(w.A,{className:"h-5 w-5"}),(0,i.jsx)("span",{children:"Report e Statistiche"})]}),(0,i.jsx)(r.BT,{children:"Visualizza report di avanzamento e statistiche del cantiere"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(l.$,{variant:"outline",className:"w-full",children:"Accedi ai Report"})})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[836,83,455,441,684,358],()=>s(51018)),_N_E=e.O()}]);