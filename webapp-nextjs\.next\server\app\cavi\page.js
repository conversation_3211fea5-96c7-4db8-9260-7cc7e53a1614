(()=>{var e={};e.id=986,e.ids=[986],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6211:(e,t,r)=>{"use strict";r.d(t,{A0:()=>o,BF:()=>l,Hj:()=>s,XI:()=>a,nA:()=>d,nd:()=>c});var n=r(60687);r(43210);var i=r(4780);function a({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,n.jsx)("table",{"data-slot":"table",className:(0,i.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,n.jsx)("thead",{"data-slot":"table-header",className:(0,i.cn)("[&_tr]:border-b",e),...t})}function l({className:e,...t}){return(0,n.jsx)("tbody",{"data-slot":"table-body",className:(0,i.cn)("[&_tr:last-child]:border-0",e),...t})}function s({className:e,...t}){return(0,n.jsx)("tr",{"data-slot":"table-row",className:(0,i.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,n.jsx)("th",{"data-slot":"table-head",className:(0,i.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,n.jsx)("td",{"data-slot":"table-cell",className:(0,i.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10698:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cavi\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\cavi\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11379:(e,t,r)=>{Promise.resolve().then(r.bind(r,64108))},12412:e=>{"use strict";e.exports=require("assert")},16023:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>s,Wu:()=>c,ZB:()=>l,Zp:()=>a,aR:()=>o});var n=r(60687);r(43210);var i=r(4780);function a({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",e),...t})}function s({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",e),...t})}},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},52270:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>f,tree:()=>c});var n=r(65239),i=r(48088),a=r(88170),o=r.n(a),l=r(30893),s={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);r.d(t,s);let c={children:["",{children:["cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,10698)),"C:\\CMS\\webapp-nextjs\\src\\app\\cavi\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\cavi\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/cavi/page",pathname:"/cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58235:(e,t,r)=>{Promise.resolve().then(r.bind(r,10698))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>u,Es:()=>p,L3:()=>h,c7:()=>f,lG:()=>l,rr:()=>m,zM:()=>s});var n=r(60687);r(43210);var i=r(37908),a=r(11860),o=r(4780);function l({...e}){return(0,n.jsx)(i.bL,{"data-slot":"dialog",...e})}function s({...e}){return(0,n.jsx)(i.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,n.jsx)(i.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...t}){return(0,n.jsx)(i.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,showCloseButton:r=!0,...l}){return(0,n.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,n.jsx)(d,{}),(0,n.jsxs)(i.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...l,children:[t,r&&(0,n.jsxs)(i.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,n.jsx)(a.A,{}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function f({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function p({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function h({className:e,...t}){return(0,n.jsx)(i.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",e),...t})}function m({className:e,...t}){return(0,n.jsx)(i.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}},64108:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>rC});var n=r(60687),i=r(43210),a=r(16189),o=r(44493),l=r(29523),s=r(96834),c=r(89667),d=r(80013),u=r(6211),f=r(63503),p=r(51215);function h(e,[t,r]){return Math.min(r,Math.max(t,e))}var m=r(70569),x=r(11273),g=r(98599),v=r(8730),b=new WeakMap;function y(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=w(t),i=n>=0?n:r+n;return i<0||i>=r?-1:i}(e,t);return -1===r?void 0:e[r]}function w(e){return e!=e||0===e?0:Math.trunc(e)}var j=i.createContext(void 0),N=r(31355),C=r(1359),_=r(32547),A=r(96963);let S=["top","right","bottom","left"],k=Math.min,R=Math.max,z=Math.round,E=Math.floor,T=e=>({x:e,y:e}),P={left:"right",right:"left",bottom:"top",top:"bottom"},L={start:"end",end:"start"};function M(e,t){return"function"==typeof e?e(t):e}function I(e){return e.split("-")[0]}function O(e){return e.split("-")[1]}function B(e){return"x"===e?"y":"x"}function D(e){return"y"===e?"height":"width"}function H(e){return["top","bottom"].includes(I(e))?"y":"x"}function V(e){return e.replace(/start|end/g,e=>L[e])}function F(e){return e.replace(/left|right|bottom|top/g,e=>P[e])}function W(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function q(e){let{x:t,y:r,width:n,height:i}=e;return{width:n,height:i,top:r,left:t,right:t+n,bottom:r+i,x:t,y:r}}function G(e,t,r){let n,{reference:i,floating:a}=e,o=H(t),l=B(H(t)),s=D(l),c=I(t),d="y"===o,u=i.x+i.width/2-a.width/2,f=i.y+i.height/2-a.height/2,p=i[s]/2-a[s]/2;switch(c){case"top":n={x:u,y:i.y-a.height};break;case"bottom":n={x:u,y:i.y+i.height};break;case"right":n={x:i.x+i.width,y:f};break;case"left":n={x:i.x-a.width,y:f};break;default:n={x:i.x,y:i.y}}switch(O(t)){case"start":n[l]-=p*(r&&d?-1:1);break;case"end":n[l]+=p*(r&&d?-1:1)}return n}let $=async(e,t,r)=>{let{placement:n="bottom",strategy:i="absolute",middleware:a=[],platform:o}=r,l=a.filter(Boolean),s=await (null==o.isRTL?void 0:o.isRTL(t)),c=await o.getElementRects({reference:e,floating:t,strategy:i}),{x:d,y:u}=G(c,n,s),f=n,p={},h=0;for(let r=0;r<l.length;r++){let{name:a,fn:m}=l[r],{x:x,y:g,data:v,reset:b}=await m({x:d,y:u,initialPlacement:n,placement:f,strategy:i,middlewareData:p,rects:c,platform:o,elements:{reference:e,floating:t}});d=null!=x?x:d,u=null!=g?g:u,p={...p,[a]:{...p[a],...v}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(c=!0===b.rects?await o.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:d,y:u}=G(c,f,s)),r=-1)}return{x:d,y:u,placement:f,strategy:i,middlewareData:p}};async function U(e,t){var r;void 0===t&&(t={});let{x:n,y:i,platform:a,rects:o,elements:l,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:f=!1,padding:p=0}=M(t,e),h=W(p),m=l[f?"floating"===u?"reference":"floating":u],x=q(await a.getClippingRect({element:null==(r=await (null==a.isElement?void 0:a.isElement(m)))||r?m:m.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(l.floating)),boundary:c,rootBoundary:d,strategy:s})),g="floating"===u?{x:n,y:i,width:o.floating.width,height:o.floating.height}:o.reference,v=await (null==a.getOffsetParent?void 0:a.getOffsetParent(l.floating)),b=await (null==a.isElement?void 0:a.isElement(v))&&await (null==a.getScale?void 0:a.getScale(v))||{x:1,y:1},y=q(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:v,strategy:s}):g);return{top:(x.top-y.top+h.top)/b.y,bottom:(y.bottom-x.bottom+h.bottom)/b.y,left:(x.left-y.left+h.left)/b.x,right:(y.right-x.right+h.right)/b.x}}function K(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Z(e){return S.some(t=>e[t]>=0)}async function X(e,t){let{placement:r,platform:n,elements:i}=e,a=await (null==n.isRTL?void 0:n.isRTL(i.floating)),o=I(r),l=O(r),s="y"===H(r),c=["left","top"].includes(o)?-1:1,d=a&&s?-1:1,u=M(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),s?{x:p*d,y:f*c}:{x:f*c,y:p*d}}function Y(){return"undefined"!=typeof window}function J(e){return et(e)?(e.nodeName||"").toLowerCase():"#document"}function Q(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ee(e){var t;return null==(t=(et(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function et(e){return!!Y()&&(e instanceof Node||e instanceof Q(e).Node)}function er(e){return!!Y()&&(e instanceof Element||e instanceof Q(e).Element)}function en(e){return!!Y()&&(e instanceof HTMLElement||e instanceof Q(e).HTMLElement)}function ei(e){return!!Y()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof Q(e).ShadowRoot)}function ea(e){let{overflow:t,overflowX:r,overflowY:n,display:i}=ed(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(i)}function eo(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function el(e){let t=es(),r=er(e)?ed(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function es(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function ec(e){return["html","body","#document"].includes(J(e))}function ed(e){return Q(e).getComputedStyle(e)}function eu(e){return er(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ef(e){if("html"===J(e))return e;let t=e.assignedSlot||e.parentNode||ei(e)&&e.host||ee(e);return ei(t)?t.host:t}function ep(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let i=function e(t){let r=ef(t);return ec(r)?t.ownerDocument?t.ownerDocument.body:t.body:en(r)&&ea(r)?r:e(r)}(e),a=i===(null==(n=e.ownerDocument)?void 0:n.body),o=Q(i);if(a){let e=eh(o);return t.concat(o,o.visualViewport||[],ea(i)?i:[],e&&r?ep(e):[])}return t.concat(i,ep(i,[],r))}function eh(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function em(e){let t=ed(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,i=en(e),a=i?e.offsetWidth:r,o=i?e.offsetHeight:n,l=z(r)!==a||z(n)!==o;return l&&(r=a,n=o),{width:r,height:n,$:l}}function ex(e){return er(e)?e:e.contextElement}function eg(e){let t=ex(e);if(!en(t))return T(1);let r=t.getBoundingClientRect(),{width:n,height:i,$:a}=em(t),o=(a?z(r.width):r.width)/n,l=(a?z(r.height):r.height)/i;return o&&Number.isFinite(o)||(o=1),l&&Number.isFinite(l)||(l=1),{x:o,y:l}}let ev=T(0);function eb(e){let t=Q(e);return es()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ev}function ey(e,t,r,n){var i;void 0===t&&(t=!1),void 0===r&&(r=!1);let a=e.getBoundingClientRect(),o=ex(e),l=T(1);t&&(n?er(n)&&(l=eg(n)):l=eg(e));let s=(void 0===(i=r)&&(i=!1),n&&(!i||n===Q(o))&&i)?eb(o):T(0),c=(a.left+s.x)/l.x,d=(a.top+s.y)/l.y,u=a.width/l.x,f=a.height/l.y;if(o){let e=Q(o),t=n&&er(n)?Q(n):n,r=e,i=eh(r);for(;i&&n&&t!==r;){let e=eg(i),t=i.getBoundingClientRect(),n=ed(i),a=t.left+(i.clientLeft+parseFloat(n.paddingLeft))*e.x,o=t.top+(i.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,u*=e.x,f*=e.y,c+=a,d+=o,i=eh(r=Q(i))}}return q({width:u,height:f,x:c,y:d})}function ew(e,t){let r=eu(e).scrollLeft;return t?t.left+r:ey(ee(e)).left+r}function ej(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:ew(e,n)),y:n.top+t.scrollTop}}function eN(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=Q(e),n=ee(e),i=r.visualViewport,a=n.clientWidth,o=n.clientHeight,l=0,s=0;if(i){a=i.width,o=i.height;let e=es();(!e||e&&"fixed"===t)&&(l=i.offsetLeft,s=i.offsetTop)}return{width:a,height:o,x:l,y:s}}(e,r);else if("document"===t)n=function(e){let t=ee(e),r=eu(e),n=e.ownerDocument.body,i=R(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=R(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),o=-r.scrollLeft+ew(e),l=-r.scrollTop;return"rtl"===ed(n).direction&&(o+=R(t.clientWidth,n.clientWidth)-i),{width:i,height:a,x:o,y:l}}(ee(e));else if(er(t))n=function(e,t){let r=ey(e,!0,"fixed"===t),n=r.top+e.clientTop,i=r.left+e.clientLeft,a=en(e)?eg(e):T(1),o=e.clientWidth*a.x,l=e.clientHeight*a.y;return{width:o,height:l,x:i*a.x,y:n*a.y}}(t,r);else{let r=eb(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return q(n)}function eC(e){return"static"===ed(e).position}function e_(e,t){if(!en(e)||"fixed"===ed(e).position)return null;if(t)return t(e);let r=e.offsetParent;return ee(e)===r&&(r=r.ownerDocument.body),r}function eA(e,t){let r=Q(e);if(eo(e))return r;if(!en(e)){let t=ef(e);for(;t&&!ec(t);){if(er(t)&&!eC(t))return t;t=ef(t)}return r}let n=e_(e,t);for(;n&&["table","td","th"].includes(J(n))&&eC(n);)n=e_(n,t);return n&&ec(n)&&eC(n)&&!el(n)?r:n||function(e){let t=ef(e);for(;en(t)&&!ec(t);){if(el(t))return t;if(eo(t))break;t=ef(t)}return null}(e)||r}let eS=async function(e){let t=this.getOffsetParent||eA,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=en(t),i=ee(t),a="fixed"===r,o=ey(e,!0,a,t),l={scrollLeft:0,scrollTop:0},s=T(0);if(n||!n&&!a)if(("body"!==J(t)||ea(i))&&(l=eu(t)),n){let e=ey(t,!0,a,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else i&&(s.x=ew(i));a&&!n&&i&&(s.x=ew(i));let c=!i||n||a?T(0):ej(i,l);return{x:o.left+l.scrollLeft-s.x-c.x,y:o.top+l.scrollTop-s.y-c.y,width:o.width,height:o.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},ek={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:i}=e,a="fixed"===i,o=ee(n),l=!!t&&eo(t.floating);if(n===o||l&&a)return r;let s={scrollLeft:0,scrollTop:0},c=T(1),d=T(0),u=en(n);if((u||!u&&!a)&&(("body"!==J(n)||ea(o))&&(s=eu(n)),en(n))){let e=ey(n);c=eg(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let f=!o||u||a?T(0):ej(o,s,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-s.scrollLeft*c.x+d.x+f.x,y:r.y*c.y-s.scrollTop*c.y+d.y+f.y}},getDocumentElement:ee,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:i}=e,a=[..."clippingAncestors"===r?eo(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=ep(e,[],!1).filter(e=>er(e)&&"body"!==J(e)),i=null,a="fixed"===ed(e).position,o=a?ef(e):e;for(;er(o)&&!ec(o);){let t=ed(o),r=el(o);r||"fixed"!==t.position||(i=null),(a?!r&&!i:!r&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||ea(o)&&!r&&function e(t,r){let n=ef(t);return!(n===r||!er(n)||ec(n))&&("fixed"===ed(n).position||e(n,r))}(e,o))?n=n.filter(e=>e!==o):i=t,o=ef(o)}return t.set(e,n),n}(t,this._c):[].concat(r),n],o=a[0],l=a.reduce((e,r)=>{let n=eN(t,r,i);return e.top=R(n.top,e.top),e.right=k(n.right,e.right),e.bottom=k(n.bottom,e.bottom),e.left=R(n.left,e.left),e},eN(t,o,i));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eA,getElementRects:eS,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=em(e);return{width:t,height:r}},getScale:eg,isElement:er,isRTL:function(e){return"rtl"===ed(e).direction}};function eR(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ez=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:i,rects:a,platform:o,elements:l,middlewareData:s}=t,{element:c,padding:d=0}=M(e,t)||{};if(null==c)return{};let u=W(d),f={x:r,y:n},p=B(H(i)),h=D(p),m=await o.getDimensions(c),x="y"===p,g=x?"clientHeight":"clientWidth",v=a.reference[h]+a.reference[p]-f[p]-a.floating[h],b=f[p]-a.reference[p],y=await (null==o.getOffsetParent?void 0:o.getOffsetParent(c)),w=y?y[g]:0;w&&await (null==o.isElement?void 0:o.isElement(y))||(w=l.floating[g]||a.floating[h]);let j=w/2-m[h]/2-1,N=k(u[x?"top":"left"],j),C=k(u[x?"bottom":"right"],j),_=w-m[h]-C,A=w/2-m[h]/2+(v/2-b/2),S=R(N,k(A,_)),z=!s.arrow&&null!=O(i)&&A!==S&&a.reference[h]/2-(A<N?N:C)-m[h]/2<0,E=z?A<N?A-N:A-_:0;return{[p]:f[p]+E,data:{[p]:S,centerOffset:A-S-E,...z&&{alignmentOffset:E}},reset:z}}}),eE=(e,t,r)=>{let n=new Map,i={platform:ek,...r},a={...i.platform,_c:n};return $(e,t,{...i,platform:a})};var eT="undefined"!=typeof document?i.useLayoutEffect:function(){};function eP(e,t){let r,n,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eP(e[n],t[n]))return!1;return!0}if((r=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,i[n]))return!1;for(n=r;0!=n--;){let r=i[n];if(("_owner"!==r||!e.$$typeof)&&!eP(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eL(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eM(e,t){let r=eL(e);return Math.round(t*r)/r}function eI(e){let t=i.useRef(e);return eT(()=>{t.current=e}),t}let eO=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ez({element:r.current,padding:n}).fn(t):{}:r?ez({element:r,padding:n}).fn(t):{}}}),eB=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:i,y:a,placement:o,middlewareData:l}=t,s=await X(t,e);return o===(null==(r=l.offset)?void 0:r.placement)&&null!=(n=l.arrow)&&n.alignmentOffset?{}:{x:i+s.x,y:a+s.y,data:{...s,placement:o}}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:i}=t,{mainAxis:a=!0,crossAxis:o=!1,limiter:l={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...s}=M(e,t),c={x:r,y:n},d=await U(t,s),u=H(I(i)),f=B(u),p=c[f],h=c[u];if(a){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+d[e],n=p-d[t];p=R(r,k(p,n))}if(o){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",r=h+d[e],n=h-d[t];h=R(r,k(h,n))}let m=l.fn({...t,[f]:p,[u]:h});return{...m,data:{x:m.x-r,y:m.y-n,enabled:{[f]:a,[u]:o}}}}}}(e),options:[e,t]}),eH=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:i,rects:a,middlewareData:o}=t,{offset:l=0,mainAxis:s=!0,crossAxis:c=!0}=M(e,t),d={x:r,y:n},u=H(i),f=B(u),p=d[f],h=d[u],m=M(l,t),x="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===f?"height":"width",t=a.reference[f]-a.floating[e]+x.mainAxis,r=a.reference[f]+a.reference[e]-x.mainAxis;p<t?p=t:p>r&&(p=r)}if(c){var g,v;let e="y"===f?"width":"height",t=["top","left"].includes(I(i)),r=a.reference[u]-a.floating[e]+(t&&(null==(g=o.offset)?void 0:g[u])||0)+(t?0:x.crossAxis),n=a.reference[u]+a.reference[e]+(t?0:(null==(v=o.offset)?void 0:v[u])||0)-(t?x.crossAxis:0);h<r?h=r:h>n&&(h=n)}return{[f]:p,[u]:h}}}}(e),options:[e,t]}),eV=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,i,a,o;let{placement:l,middlewareData:s,rects:c,initialPlacement:d,platform:u,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:v=!0,...b}=M(e,t);if(null!=(r=s.arrow)&&r.alignmentOffset)return{};let y=I(l),w=H(d),j=I(d)===d,N=await (null==u.isRTL?void 0:u.isRTL(f.floating)),C=m||(j||!v?[F(d)]:function(e){let t=F(e);return[V(e),t,V(t)]}(d)),_="none"!==g;!m&&_&&C.push(...function(e,t,r,n){let i=O(e),a=function(e,t,r){let n=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(r)return t?i:n;return t?n:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(I(e),"start"===r,n);return i&&(a=a.map(e=>e+"-"+i),t&&(a=a.concat(a.map(V)))),a}(d,v,g,N));let A=[d,...C],S=await U(t,b),k=[],R=(null==(n=s.flip)?void 0:n.overflows)||[];if(p&&k.push(S[y]),h){let e=function(e,t,r){void 0===r&&(r=!1);let n=O(e),i=B(H(e)),a=D(i),o="x"===i?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[a]>t.floating[a]&&(o=F(o)),[o,F(o)]}(l,c,N);k.push(S[e[0]],S[e[1]])}if(R=[...R,{placement:l,overflows:k}],!k.every(e=>e<=0)){let e=((null==(i=s.flip)?void 0:i.index)||0)+1,t=A[e];if(t&&("alignment"!==h||w===H(t)||R.every(e=>e.overflows[0]>0&&H(e.placement)===w)))return{data:{index:e,overflows:R},reset:{placement:t}};let r=null==(a=R.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!r)switch(x){case"bestFit":{let e=null==(o=R.filter(e=>{if(_){let t=H(e.placement);return t===w||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:o[0];e&&(r=e);break}case"initialPlacement":r=d}if(l!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),eF=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let i,a,{placement:o,rects:l,platform:s,elements:c}=t,{apply:d=()=>{},...u}=M(e,t),f=await U(t,u),p=I(o),h=O(o),m="y"===H(o),{width:x,height:g}=l.floating;"top"===p||"bottom"===p?(i=p,a=h===(await (null==s.isRTL?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(a=p,i="end"===h?"top":"bottom");let v=g-f.top-f.bottom,b=x-f.left-f.right,y=k(g-f[i],v),w=k(x-f[a],b),j=!t.middlewareData.shift,N=y,C=w;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(C=b),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(N=v),j&&!h){let e=R(f.left,0),t=R(f.right,0),r=R(f.top,0),n=R(f.bottom,0);m?C=x-2*(0!==e||0!==t?e+t:R(f.left,f.right)):N=g-2*(0!==r||0!==n?r+n:R(f.top,f.bottom))}await d({...t,availableWidth:C,availableHeight:N});let _=await s.getDimensions(c.floating);return x!==_.width||g!==_.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eW=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...i}=M(e,t);switch(n){case"referenceHidden":{let e=K(await U(t,{...i,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:Z(e)}}}case"escaped":{let e=K(await U(t,{...i,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:Z(e)}}}default:return{}}}}}(e),options:[e,t]}),eq=(e,t)=>({...eO(e),options:[e,t]});var eG=r(14163),e$=i.forwardRef((e,t)=>{let{children:r,width:i=10,height:a=5,...o}=e;return(0,n.jsx)(eG.sG.svg,{...o,ref:t,width:i,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,n.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e$.displayName="Arrow";var eU=r(13495),eK=r(66156),eZ="Popper",[eX,eY]=(0,x.A)(eZ),[eJ,eQ]=eX(eZ),e0=e=>{let{__scopePopper:t,children:r}=e,[a,o]=i.useState(null);return(0,n.jsx)(eJ,{scope:t,anchor:a,onAnchorChange:o,children:r})};e0.displayName=eZ;var e1="PopperAnchor",e2=i.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:a,...o}=e,l=eQ(e1,r),s=i.useRef(null),c=(0,g.s)(t,s);return i.useEffect(()=>{l.onAnchorChange(a?.current||s.current)}),a?null:(0,n.jsx)(eG.sG.div,{...o,ref:c})});e2.displayName=e1;var e5="PopperContent",[e6,e8]=eX(e5),e4=i.forwardRef((e,t)=>{let{__scopePopper:r,side:a="bottom",sideOffset:o=0,align:l="center",alignOffset:s=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:h="partial",hideWhenDetached:m=!1,updatePositionStrategy:x="optimized",onPlaced:v,...b}=e,y=eQ(e5,r),[w,j]=i.useState(null),N=(0,g.s)(t,e=>j(e)),[C,_]=i.useState(null),A=function(e){let[t,r]=i.useState(void 0);return(0,eK.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;r({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(C),S=A?.width??0,z=A?.height??0,T="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},P=Array.isArray(u)?u:[u],L=P.length>0,M={padding:T,boundary:P.filter(te),altBoundary:L},{refs:I,floatingStyles:O,placement:B,isPositioned:D,middlewareData:H}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:a,elements:{reference:o,floating:l}={},transform:s=!0,whileElementsMounted:c,open:d}=e,[u,f]=i.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=i.useState(n);eP(h,n)||m(n);let[x,g]=i.useState(null),[v,b]=i.useState(null),y=i.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),w=i.useCallback(e=>{e!==_.current&&(_.current=e,b(e))},[]),j=o||x,N=l||v,C=i.useRef(null),_=i.useRef(null),A=i.useRef(u),S=null!=c,k=eI(c),R=eI(a),z=eI(d),E=i.useCallback(()=>{if(!C.current||!_.current)return;let e={placement:t,strategy:r,middleware:h};R.current&&(e.platform=R.current),eE(C.current,_.current,e).then(e=>{let t={...e,isPositioned:!1!==z.current};T.current&&!eP(A.current,t)&&(A.current=t,p.flushSync(()=>{f(t)}))})},[h,t,r,R,z]);eT(()=>{!1===d&&A.current.isPositioned&&(A.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[d]);let T=i.useRef(!1);eT(()=>(T.current=!0,()=>{T.current=!1}),[]),eT(()=>{if(j&&(C.current=j),N&&(_.current=N),j&&N){if(k.current)return k.current(j,N,E);E()}},[j,N,E,k,S]);let P=i.useMemo(()=>({reference:C,floating:_,setReference:y,setFloating:w}),[y,w]),L=i.useMemo(()=>({reference:j,floating:N}),[j,N]),M=i.useMemo(()=>{let e={position:r,left:0,top:0};if(!L.floating)return e;let t=eM(L.floating,u.x),n=eM(L.floating,u.y);return s?{...e,transform:"translate("+t+"px, "+n+"px)",...eL(L.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,s,L.floating,u.x,u.y]);return i.useMemo(()=>({...u,update:E,refs:P,elements:L,floatingStyles:M}),[u,E,P,L,M])}({strategy:"fixed",placement:a+("center"!==l?"-"+l:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let i;void 0===n&&(n={});let{ancestorScroll:a=!0,ancestorResize:o=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,d=ex(e),u=a||o?[...d?ep(d):[],...ep(t)]:[];u.forEach(e=>{a&&e.addEventListener("scroll",r,{passive:!0}),o&&e.addEventListener("resize",r)});let f=d&&s?function(e,t){let r,n=null,i=ee(e);function a(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function o(l,s){void 0===l&&(l=!1),void 0===s&&(s=1),a();let c=e.getBoundingClientRect(),{left:d,top:u,width:f,height:p}=c;if(l||t(),!f||!p)return;let h=E(u),m=E(i.clientWidth-(d+f)),x={rootMargin:-h+"px "+-m+"px "+-E(i.clientHeight-(u+p))+"px "+-E(d)+"px",threshold:R(0,k(1,s))||1},g=!0;function v(t){let n=t[0].intersectionRatio;if(n!==s){if(!g)return o();n?o(!1,n):r=setTimeout(()=>{o(!1,1e-7)},1e3)}1!==n||eR(c,e.getBoundingClientRect())||o(),g=!1}try{n=new IntersectionObserver(v,{...x,root:i.ownerDocument})}catch(e){n=new IntersectionObserver(v,x)}n.observe(e)}(!0),a}(d,r):null,p=-1,h=null;l&&(h=new ResizeObserver(e=>{let[n]=e;n&&n.target===d&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),r()}),d&&!c&&h.observe(d),h.observe(t));let m=c?ey(e):null;return c&&function t(){let n=ey(e);m&&!eR(m,n)&&r(),m=n,i=requestAnimationFrame(t)}(),r(),()=>{var e;u.forEach(e=>{a&&e.removeEventListener("scroll",r),o&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===x}),elements:{reference:y.anchor},middleware:[eB({mainAxis:o+z,alignmentAxis:s}),d&&eD({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?eH():void 0,...M}),d&&eV({...M}),eF({...M,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:i,height:a}=t.reference,o=e.floating.style;o.setProperty("--radix-popper-available-width",`${r}px`),o.setProperty("--radix-popper-available-height",`${n}px`),o.setProperty("--radix-popper-anchor-width",`${i}px`),o.setProperty("--radix-popper-anchor-height",`${a}px`)}}),C&&eq({element:C,padding:c}),tt({arrowWidth:S,arrowHeight:z}),m&&eW({strategy:"referenceHidden",...M})]}),[V,F]=tr(B),W=(0,eU.c)(v);(0,eK.N)(()=>{D&&W?.()},[D,W]);let q=H.arrow?.x,G=H.arrow?.y,$=H.arrow?.centerOffset!==0,[U,K]=i.useState();return(0,eK.N)(()=>{w&&K(window.getComputedStyle(w).zIndex)},[w]),(0,n.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:D?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[H.transformOrigin?.x,H.transformOrigin?.y].join(" "),...H.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,n.jsx)(e6,{scope:r,placedSide:V,onArrowChange:_,arrowX:q,arrowY:G,shouldHideArrow:$,children:(0,n.jsx)(eG.sG.div,{"data-side":V,"data-align":F,...b,ref:N,style:{...b.style,animation:D?void 0:"none"}})})})});e4.displayName=e5;var e3="PopperArrow",e9={top:"bottom",right:"left",bottom:"top",left:"right"},e7=i.forwardRef(function(e,t){let{__scopePopper:r,...i}=e,a=e8(e3,r),o=e9[a.placedSide];return(0,n.jsx)("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:(0,n.jsx)(e$,{...i,ref:t,style:{...i.style,display:"block"}})})});function te(e){return null!==e}e7.displayName=e3;var tt=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:i}=t,a=i.arrow?.centerOffset!==0,o=a?0:e.arrowWidth,l=a?0:e.arrowHeight,[s,c]=tr(r),d={start:"0%",center:"50%",end:"100%"}[c],u=(i.arrow?.x??0)+o/2,f=(i.arrow?.y??0)+l/2,p="",h="";return"bottom"===s?(p=a?d:`${u}px`,h=`${-l}px`):"top"===s?(p=a?d:`${u}px`,h=`${n.floating.height+l}px`):"right"===s?(p=`${-l}px`,h=a?d:`${f}px`):"left"===s&&(p=`${n.floating.width+l}px`,h=a?d:`${f}px`),{data:{x:p,y:h}}}});function tr(e){let[t,r="center"]=e.split("-");return[t,r]}var tn=r(25028),ti=r(65551),ta=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});i.forwardRef((e,t)=>(0,n.jsx)(eG.sG.span,{...e,ref:t,style:{...ta,...e.style}})).displayName="VisuallyHidden";var to=r(63376),tl=r(42247),ts=[" ","Enter","ArrowUp","ArrowDown"],tc=[" ","Enter"],td="Select",[tu,tf,tp]=function(e){let t=e+"CollectionProvider",[r,a]=(0,x.A)(t),[o,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:r}=e,a=i.useRef(null),l=i.useRef(new Map).current;return(0,n.jsx)(o,{scope:t,itemMap:l,collectionRef:a,children:r})};s.displayName=t;let c=e+"CollectionSlot",d=(0,v.TL)(c),u=i.forwardRef((e,t)=>{let{scope:r,children:i}=e,a=l(c,r),o=(0,g.s)(t,a.collectionRef);return(0,n.jsx)(d,{ref:o,children:i})});u.displayName=c;let f=e+"CollectionItemSlot",p="data-radix-collection-item",h=(0,v.TL)(f),m=i.forwardRef((e,t)=>{let{scope:r,children:a,...o}=e,s=i.useRef(null),c=(0,g.s)(t,s),d=l(f,r);return i.useEffect(()=>(d.itemMap.set(s,{ref:s,...o}),()=>void d.itemMap.delete(s))),(0,n.jsx)(h,{...{[p]:""},ref:c,children:a})});return m.displayName=f,[{Provider:s,Slot:u,ItemSlot:m},function(t){let r=l(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}(td),[th,tm]=(0,x.A)(td,[tp,eY]),tx=eY(),[tg,tv]=th(td),[tb,ty]=th(td),tw=e=>{let{__scopeSelect:t,children:r,open:a,defaultOpen:o,onOpenChange:l,value:s,defaultValue:c,onValueChange:d,dir:u,name:f,autoComplete:p,disabled:h,required:m,form:x}=e,g=tx(t),[v,b]=i.useState(null),[y,w]=i.useState(null),[N,C]=i.useState(!1),_=function(e){let t=i.useContext(j);return e||t||"ltr"}(u),[S,k]=(0,ti.i)({prop:a,defaultProp:o??!1,onChange:l,caller:td}),[R,z]=(0,ti.i)({prop:s,defaultProp:c,onChange:d,caller:td}),E=i.useRef(null),T=!v||x||!!v.closest("form"),[P,L]=i.useState(new Set),M=Array.from(P).map(e=>e.props.value).join(";");return(0,n.jsx)(e0,{...g,children:(0,n.jsxs)(tg,{required:m,scope:t,trigger:v,onTriggerChange:b,valueNode:y,onValueNodeChange:w,valueNodeHasChildren:N,onValueNodeHasChildrenChange:C,contentId:(0,A.B)(),value:R,onValueChange:z,open:S,onOpenChange:k,dir:_,triggerPointerDownPosRef:E,disabled:h,children:[(0,n.jsx)(tu.Provider,{scope:t,children:(0,n.jsx)(tb,{scope:e.__scopeSelect,onNativeOptionAdd:i.useCallback(e=>{L(t=>new Set(t).add(e))},[]),onNativeOptionRemove:i.useCallback(e=>{L(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),T?(0,n.jsxs)(t6,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:R,onChange:e=>z(e.target.value),disabled:h,form:x,children:[void 0===R?(0,n.jsx)("option",{value:""}):null,Array.from(P)]},M):null]})})};tw.displayName=td;var tj="SelectTrigger",tN=i.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:a=!1,...o}=e,l=tx(r),s=tv(tj,r),c=s.disabled||a,d=(0,g.s)(t,s.onTriggerChange),u=tf(r),f=i.useRef("touch"),[p,h,x]=t4(e=>{let t=u().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=t3(t,e,r);void 0!==n&&s.onValueChange(n.value)}),v=e=>{c||(s.onOpenChange(!0),x()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,n.jsx)(e2,{asChild:!0,...l,children:(0,n.jsx)(eG.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":t8(s.value)?"":void 0,...o,ref:d,onClick:(0,m.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&v(e)}),onPointerDown:(0,m.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(v(e),e.preventDefault())}),onKeyDown:(0,m.m)(o.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&ts.includes(e.key)&&(v(),e.preventDefault())})})})});tN.displayName=tj;var tC="SelectValue",t_=i.forwardRef((e,t)=>{let{__scopeSelect:r,className:i,style:a,children:o,placeholder:l="",...s}=e,c=tv(tC,r),{onValueNodeHasChildrenChange:d}=c,u=void 0!==o,f=(0,g.s)(t,c.onValueNodeChange);return(0,eK.N)(()=>{d(u)},[d,u]),(0,n.jsx)(eG.sG.span,{...s,ref:f,style:{pointerEvents:"none"},children:t8(c.value)?(0,n.jsx)(n.Fragment,{children:l}):o})});t_.displayName=tC;var tA=i.forwardRef((e,t)=>{let{__scopeSelect:r,children:i,...a}=e;return(0,n.jsx)(eG.sG.span,{"aria-hidden":!0,...a,ref:t,children:i||"▼"})});tA.displayName="SelectIcon";var tS=e=>(0,n.jsx)(tn.Z,{asChild:!0,...e});tS.displayName="SelectPortal";var tk="SelectContent",tR=i.forwardRef((e,t)=>{let r=tv(tk,e.__scopeSelect),[a,o]=i.useState();return((0,eK.N)(()=>{o(new DocumentFragment)},[]),r.open)?(0,n.jsx)(tP,{...e,ref:t}):a?p.createPortal((0,n.jsx)(tz,{scope:e.__scopeSelect,children:(0,n.jsx)(tu.Slot,{scope:e.__scopeSelect,children:(0,n.jsx)("div",{children:e.children})})}),a):null});tR.displayName=tk;var[tz,tE]=th(tk),tT=(0,v.TL)("SelectContent.RemoveScroll"),tP=i.forwardRef((e,t)=>{let{__scopeSelect:r,position:a="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:l,onPointerDownOutside:s,side:c,sideOffset:d,align:u,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:x,sticky:v,hideWhenDetached:b,avoidCollisions:y,...w}=e,j=tv(tk,r),[A,S]=i.useState(null),[k,R]=i.useState(null),z=(0,g.s)(t,e=>S(e)),[E,T]=i.useState(null),[P,L]=i.useState(null),M=tf(r),[I,O]=i.useState(!1),B=i.useRef(!1);i.useEffect(()=>{if(A)return(0,to.Eq)(A)},[A]),(0,C.Oh)();let D=i.useCallback(e=>{let[t,...r]=M().map(e=>e.ref.current),[n]=r.slice(-1),i=document.activeElement;for(let r of e)if(r===i||(r?.scrollIntoView({block:"nearest"}),r===t&&k&&(k.scrollTop=0),r===n&&k&&(k.scrollTop=k.scrollHeight),r?.focus(),document.activeElement!==i))return},[M,k]),H=i.useCallback(()=>D([E,A]),[D,E,A]);i.useEffect(()=>{I&&H()},[I,H]);let{onOpenChange:V,triggerPointerDownPosRef:F}=j;i.useEffect(()=>{if(A){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(F.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(F.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():A.contains(r.target)||V(!1),document.removeEventListener("pointermove",t),F.current=null};return null!==F.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[A,V,F]),i.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[W,q]=t4(e=>{let t=M().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=t3(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),G=i.useCallback((e,t,r)=>{let n=!B.current&&!r;(void 0!==j.value&&j.value===t||n)&&(T(e),n&&(B.current=!0))},[j.value]),$=i.useCallback(()=>A?.focus(),[A]),U=i.useCallback((e,t,r)=>{let n=!B.current&&!r;(void 0!==j.value&&j.value===t||n)&&L(e)},[j.value]),K="popper"===a?tM:tL,Z=K===tM?{side:c,sideOffset:d,align:u,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:x,sticky:v,hideWhenDetached:b,avoidCollisions:y}:{};return(0,n.jsx)(tz,{scope:r,content:A,viewport:k,onViewportChange:R,itemRefCallback:G,selectedItem:E,onItemLeave:$,itemTextRefCallback:U,focusSelectedItem:H,selectedItemText:P,position:a,isPositioned:I,searchRef:W,children:(0,n.jsx)(tl.A,{as:tT,allowPinchZoom:!0,children:(0,n.jsx)(_.n,{asChild:!0,trapped:j.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,m.m)(o,e=>{j.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,n.jsx)(N.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:(0,n.jsx)(K,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:e=>e.preventDefault(),...w,...Z,onPlaced:()=>O(!0),ref:z,style:{display:"flex",flexDirection:"column",outline:"none",...w.style},onKeyDown:(0,m.m)(w.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>D(t)),e.preventDefault()}})})})})})})});tP.displayName="SelectContentImpl";var tL=i.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:a,...o}=e,l=tv(tk,r),s=tE(tk,r),[c,d]=i.useState(null),[u,f]=i.useState(null),p=(0,g.s)(t,e=>f(e)),m=tf(r),x=i.useRef(!1),v=i.useRef(!0),{viewport:b,selectedItem:y,selectedItemText:w,focusSelectedItem:j}=s,N=i.useCallback(()=>{if(l.trigger&&l.valueNode&&c&&u&&b&&y&&w){let e=l.trigger.getBoundingClientRect(),t=u.getBoundingClientRect(),r=l.valueNode.getBoundingClientRect(),n=w.getBoundingClientRect();if("rtl"!==l.dir){let i=n.left-t.left,a=r.left-i,o=e.left-a,l=e.width+o,s=Math.max(l,t.width),d=h(a,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.left=d+"px"}else{let i=t.right-n.right,a=window.innerWidth-r.right-i,o=window.innerWidth-e.right-a,l=e.width+o,s=Math.max(l,t.width),d=h(a,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.right=d+"px"}let i=m(),o=window.innerHeight-20,s=b.scrollHeight,d=window.getComputedStyle(u),f=parseInt(d.borderTopWidth,10),p=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),v=f+p+s+parseInt(d.paddingBottom,10)+g,j=Math.min(5*y.offsetHeight,v),N=window.getComputedStyle(b),C=parseInt(N.paddingTop,10),_=parseInt(N.paddingBottom,10),A=e.top+e.height/2-10,S=y.offsetHeight/2,k=f+p+(y.offsetTop+S);if(k<=A){let e=i.length>0&&y===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(o-A,S+(e?_:0)+(u.clientHeight-b.offsetTop-b.offsetHeight)+g);c.style.height=k+t+"px"}else{let e=i.length>0&&y===i[0].ref.current;c.style.top="0px";let t=Math.max(A,f+b.offsetTop+(e?C:0)+S);c.style.height=t+(v-k)+"px",b.scrollTop=k-A+b.offsetTop}c.style.margin="10px 0",c.style.minHeight=j+"px",c.style.maxHeight=o+"px",a?.(),requestAnimationFrame(()=>x.current=!0)}},[m,l.trigger,l.valueNode,c,u,b,y,w,l.dir,a]);(0,eK.N)(()=>N(),[N]);let[C,_]=i.useState();(0,eK.N)(()=>{u&&_(window.getComputedStyle(u).zIndex)},[u]);let A=i.useCallback(e=>{e&&!0===v.current&&(N(),j?.(),v.current=!1)},[N,j]);return(0,n.jsx)(tI,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:x,onScrollButtonChange:A,children:(0,n.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,n.jsx)(eG.sG.div,{...o,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});tL.displayName="SelectItemAlignedPosition";var tM=i.forwardRef((e,t)=>{let{__scopeSelect:r,align:i="start",collisionPadding:a=10,...o}=e,l=tx(r);return(0,n.jsx)(e4,{...l,...o,ref:t,align:i,collisionPadding:a,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tM.displayName="SelectPopperPosition";var[tI,tO]=th(tk,{}),tB="SelectViewport",tD=i.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:a,...o}=e,l=tE(tB,r),s=tO(tB,r),c=(0,g.s)(t,l.onViewportChange),d=i.useRef(0);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,n.jsx)(tu.Slot,{scope:r,children:(0,n.jsx)(eG.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,m.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if(n?.current&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,i=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(i<n){let a=i+e,o=Math.min(n,a),l=a-o;r.style.height=o+"px","0px"===r.style.bottom&&(t.scrollTop=l>0?l:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tD.displayName=tB;var tH="SelectGroup",[tV,tF]=th(tH);i.forwardRef((e,t)=>{let{__scopeSelect:r,...i}=e,a=(0,A.B)();return(0,n.jsx)(tV,{scope:r,id:a,children:(0,n.jsx)(eG.sG.div,{role:"group","aria-labelledby":a,...i,ref:t})})}).displayName=tH;var tW="SelectLabel";i.forwardRef((e,t)=>{let{__scopeSelect:r,...i}=e,a=tF(tW,r);return(0,n.jsx)(eG.sG.div,{id:a.id,...i,ref:t})}).displayName=tW;var tq="SelectItem",[tG,t$]=th(tq),tU=i.forwardRef((e,t)=>{let{__scopeSelect:r,value:a,disabled:o=!1,textValue:l,...s}=e,c=tv(tq,r),d=tE(tq,r),u=c.value===a,[f,p]=i.useState(l??""),[h,x]=i.useState(!1),v=(0,g.s)(t,e=>d.itemRefCallback?.(e,a,o)),b=(0,A.B)(),y=i.useRef("touch"),w=()=>{o||(c.onValueChange(a),c.onOpenChange(!1))};if(""===a)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,n.jsx)(tG,{scope:r,value:a,disabled:o,textId:b,isSelected:u,onItemTextChange:i.useCallback(e=>{p(t=>t||(e?.textContent??"").trim())},[]),children:(0,n.jsx)(tu.ItemSlot,{scope:r,value:a,disabled:o,textValue:f,children:(0,n.jsx)(eG.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":h?"":void 0,"aria-selected":u&&h,"data-state":u?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:v,onFocus:(0,m.m)(s.onFocus,()=>x(!0)),onBlur:(0,m.m)(s.onBlur,()=>x(!1)),onClick:(0,m.m)(s.onClick,()=>{"mouse"!==y.current&&w()}),onPointerUp:(0,m.m)(s.onPointerUp,()=>{"mouse"===y.current&&w()}),onPointerDown:(0,m.m)(s.onPointerDown,e=>{y.current=e.pointerType}),onPointerMove:(0,m.m)(s.onPointerMove,e=>{y.current=e.pointerType,o?d.onItemLeave?.():"mouse"===y.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,m.m)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,m.m)(s.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(tc.includes(e.key)&&w()," "===e.key&&e.preventDefault())})})})})});tU.displayName=tq;var tK="SelectItemText",tZ=i.forwardRef((e,t)=>{let{__scopeSelect:r,className:a,style:o,...l}=e,s=tv(tK,r),c=tE(tK,r),d=t$(tK,r),u=ty(tK,r),[f,h]=i.useState(null),m=(0,g.s)(t,e=>h(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),x=f?.textContent,v=i.useMemo(()=>(0,n.jsx)("option",{value:d.value,disabled:d.disabled,children:x},d.value),[d.disabled,d.value,x]),{onNativeOptionAdd:b,onNativeOptionRemove:y}=u;return(0,eK.N)(()=>(b(v),()=>y(v)),[b,y,v]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(eG.sG.span,{id:d.textId,...l,ref:m}),d.isSelected&&s.valueNode&&!s.valueNodeHasChildren?p.createPortal(l.children,s.valueNode):null]})});tZ.displayName=tK;var tX="SelectItemIndicator",tY=i.forwardRef((e,t)=>{let{__scopeSelect:r,...i}=e;return t$(tX,r).isSelected?(0,n.jsx)(eG.sG.span,{"aria-hidden":!0,...i,ref:t}):null});tY.displayName=tX;var tJ="SelectScrollUpButton",tQ=i.forwardRef((e,t)=>{let r=tE(tJ,e.__scopeSelect),a=tO(tJ,e.__scopeSelect),[o,l]=i.useState(!1),s=(0,g.s)(t,a.onScrollButtonChange);return(0,eK.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){l(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,n.jsx)(t2,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});tQ.displayName=tJ;var t0="SelectScrollDownButton",t1=i.forwardRef((e,t)=>{let r=tE(t0,e.__scopeSelect),a=tO(t0,e.__scopeSelect),[o,l]=i.useState(!1),s=(0,g.s)(t,a.onScrollButtonChange);return(0,eK.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,n.jsx)(t2,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});t1.displayName=t0;var t2=i.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:a,...o}=e,l=tE("SelectScrollButton",r),s=i.useRef(null),c=tf(r),d=i.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return i.useEffect(()=>()=>d(),[d]),(0,eK.N)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,n.jsx)(eG.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,m.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(a,50))}),onPointerMove:(0,m.m)(o.onPointerMove,()=>{l.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(a,50))}),onPointerLeave:(0,m.m)(o.onPointerLeave,()=>{d()})})});i.forwardRef((e,t)=>{let{__scopeSelect:r,...i}=e;return(0,n.jsx)(eG.sG.div,{"aria-hidden":!0,...i,ref:t})}).displayName="SelectSeparator";var t5="SelectArrow";i.forwardRef((e,t)=>{let{__scopeSelect:r,...i}=e,a=tx(r),o=tv(t5,r),l=tE(t5,r);return o.open&&"popper"===l.position?(0,n.jsx)(e7,{...a,...i,ref:t}):null}).displayName=t5;var t6=i.forwardRef(({__scopeSelect:e,value:t,...r},a)=>{let o=i.useRef(null),l=(0,g.s)(a,o),s=function(e){let t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return i.useEffect(()=>{let e=o.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[s,t]),(0,n.jsx)(eG.sG.select,{...r,style:{...ta,...r.style},ref:l,defaultValue:t})});function t8(e){return""===e||void 0===e}function t4(e){let t=(0,eU.c)(e),r=i.useRef(""),n=i.useRef(0),a=i.useCallback(e=>{let i=r.current+e;t(i),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(i)},[t]),o=i.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return i.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,a,o]}function t3(e,t,r){var n,i;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=r?e.indexOf(r):-1,l=(n=e,i=Math.max(o,0),n.map((e,t)=>n[(i+t)%n.length]));1===a.length&&(l=l.filter(e=>e!==r));let s=l.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return s!==r?s:void 0}t6.displayName="SelectBubbleInput";var t9=r(62688);let t7=(0,t9.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),re=(0,t9.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),rt=(0,t9.A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var rr=r(4780);function rn({...e}){return(0,n.jsx)(tw,{"data-slot":"select",...e})}function ri({...e}){return(0,n.jsx)(t_,{"data-slot":"select-value",...e})}function ra({className:e,size:t="default",children:r,...i}){return(0,n.jsxs)(tN,{"data-slot":"select-trigger","data-size":t,className:(0,rr.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[r,(0,n.jsx)(tA,{asChild:!0,children:(0,n.jsx)(t7,{className:"size-4 opacity-50"})})]})}function ro({className:e,children:t,position:r="popper",...i}){return(0,n.jsx)(tS,{children:(0,n.jsxs)(tR,{"data-slot":"select-content",className:(0,rr.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...i,children:[(0,n.jsx)(rs,{}),(0,n.jsx)(tD,{className:(0,rr.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,n.jsx)(rc,{})]})})}function rl({className:e,children:t,...r}){return(0,n.jsxs)(tU,{"data-slot":"select-item",className:(0,rr.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,n.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(tY,{children:(0,n.jsx)(re,{className:"size-4"})})}),(0,n.jsx)(tZ,{children:t})]})}function rs({className:e,...t}){return(0,n.jsx)(tQ,{"data-slot":"select-scroll-up-button",className:(0,rr.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(rt,{className:"size-4"})})}function rc({className:e,...t}){return(0,n.jsx)(t1,{"data-slot":"select-scroll-down-button",className:(0,rr.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(t7,{className:"size-4"})})}var rd=r(63213),ru=r(62185),rf=r(23361),rp=r(31158),rh=r(16023),rm=r(96474),rx=r(5336),rg=r(48730),rv=r(93613),rb=r(99270),ry=r(41862),rw=r(13861),rj=r(63143),rN=r(88233);function rC(){let[e,t]=(0,i.useState)(""),[r,p]=(0,i.useState)("all"),[h,m]=(0,i.useState)([]),[x,g]=(0,i.useState)(!0),[v,b]=(0,i.useState)(""),[y,w]=(0,i.useState)(null),[j,N]=(0,i.useState)(!1),[C,_]=(0,i.useState)(!1),[A,S]=(0,i.useState)(!1),[k,R]=(0,i.useState)(!1),[z,E]=(0,i.useState)([]),[T,P]=(0,i.useState)({id_cavo:"",revisione_ufficiale:"",tipologia:"",n_conduttori:"",sezione:"",ubicazione_partenza:"",ubicazione_arrivo:"",metri_teorici:0,responsabile_posa:"",metri_posati:0,id_bobina:""}),{user:L,cantiere:M}=(0,rd.A)();(0,a.useRouter)();let[I,O]=(0,i.useState)(0),B=async()=>{try{if(g(!0),b(""),!I)return void b("Cantiere non selezionato");let t=await ru.At.getCavi(I,{search:e,stato_installazione:"all"===r?void 0:r});m(t)}catch(e){console.error("Errore caricamento cavi:",e),b(e.response?.data?.detail||"Errore durante il caricamento dei cavi")}finally{g(!1)}},D=async()=>{if(y)try{await ru.At.updateMetriPosati(I,y.id_cavo,T.metri_posati),S(!1),w(null),V(),B()}catch(e){console.error("Errore nell'aggiornamento metri:",e),b("Errore nell'aggiornamento dei metri")}},H=async()=>{if(y)try{await ru.At.updateBobina(I,y.id_cavo,T.id_bobina),R(!1),w(null),V(),B()}catch(e){console.error("Errore nell'aggiornamento bobina:",e),b("Errore nell'aggiornamento della bobina")}},V=()=>{P({id_cavo:"",revisione_ufficiale:"",tipologia:"",n_conduttori:"",sezione:"",ubicazione_partenza:"",ubicazione_arrivo:"",metri_teorici:0,responsabile_posa:"",metri_posati:0,id_bobina:""})},F=e=>{w(e),P({id_cavo:e.id_cavo||"",revisione_ufficiale:e.revisione_ufficiale||"",tipologia:e.tipologia||"",n_conduttori:e.n_conduttori||"",sezione:e.sezione||"",ubicazione_partenza:e.ubicazione_partenza||"",ubicazione_arrivo:e.ubicazione_arrivo||"",metri_teorici:e.metri_teorici||0,responsabile_posa:e.responsabile_posa||"",metri_posati:e.metratura_reale||0,id_bobina:e.id_bobina||""}),_(!0)},W=e=>{w(e),P({...T,metri_posati:e.metratura_reale||0,id_bobina:e.id_bobina||""}),S(!0)},q=e=>{w(e),P({...T,id_bobina:e.id_bobina||""}),R(!0)},G=e=>{switch(e){case"installato":return(0,n.jsx)(s.E,{className:"bg-green-100 text-green-800",children:"Installato"});case"in_corso":return(0,n.jsx)(s.E,{className:"bg-yellow-100 text-yellow-800",children:"In Corso"});case"non_installato":case"":case null:case void 0:return(0,n.jsx)(s.E,{className:"bg-gray-100 text-gray-800",children:"Non Installato"});default:return(0,n.jsx)(s.E,{variant:"secondary",children:e})}},$=e=>{switch(e){case 3:return(0,n.jsx)(s.E,{className:"bg-green-100 text-green-800",children:"Collegato"});case 1:case 2:return(0,n.jsx)(s.E,{className:"bg-yellow-100 text-yellow-800",children:"Parziale"});case 0:case null:case void 0:return(0,n.jsx)(s.E,{className:"bg-gray-100 text-gray-800",children:"Non Collegato"});default:return(0,n.jsxs)(s.E,{variant:"secondary",children:["Stato ",e]})}},U=e=>e.data_certificazione?(0,n.jsx)(s.E,{className:"bg-green-100 text-green-800",children:"Certificato"}):e.comanda_certificazione?(0,n.jsx)(s.E,{className:"bg-yellow-100 text-yellow-800",children:"In Corso"}):(0,n.jsx)(s.E,{className:"bg-gray-100 text-gray-800",children:"Non Certificato"}),K=h.filter(t=>{let n=t.id_cavo?.toLowerCase().includes(e.toLowerCase())||t.tipologia?.toLowerCase().includes(e.toLowerCase())||t.n_conduttori?.toLowerCase().includes(e.toLowerCase())||t.sezione?.toLowerCase().includes(e.toLowerCase()),i="all"===r||t.stato_installazione===r;return n&&i}),Z={totali:h.length,installati:h.filter(e=>e.metratura_reale&&e.metratura_reale>0).length,in_corso:h.filter(e=>e.comanda_posa&&!e.data_posa).length,non_installati:h.filter(e=>!e.metratura_reale||0===e.metratura_reale).length};return(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,n.jsx)(rf.A,{className:"h-8 w-8 text-blue-600"}),"Gestione Cavi"]}),(0,n.jsx)("p",{className:"text-slate-600 mt-1",children:"Visualizzazione e gestione completa dei cavi del cantiere"})]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,n.jsx)(rp.A,{className:"h-4 w-4 mr-2"}),"Esporta"]}),(0,n.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,n.jsx)(rh.A,{className:"h-4 w-4 mr-2"}),"Importa"]}),(0,n.jsxs)(l.$,{size:"sm",children:[(0,n.jsx)(rm.A,{className:"h-4 w-4 mr-2"}),"Nuovo Cavo"]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,n.jsx)(o.Zp,{children:(0,n.jsx)(o.Wu,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:Z.totali})]}),(0,n.jsx)(rf.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,n.jsx)(o.Zp,{children:(0,n.jsx)(o.Wu,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-slate-600",children:"Installati"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-green-600",children:Z.installati})]}),(0,n.jsx)(rx.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,n.jsx)(o.Zp,{children:(0,n.jsx)(o.Wu,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-slate-600",children:"In Corso"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:Z.in_corso})]}),(0,n.jsx)(rg.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,n.jsx)(o.Zp,{children:(0,n.jsx)(o.Wu,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-slate-600",children:"Da Installare"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-600",children:Z.non_installati})]}),(0,n.jsx)(rv.A,{className:"h-8 w-8 text-gray-500"})]})})})]}),(0,n.jsxs)(o.Zp,{children:[(0,n.jsx)(o.aR,{children:(0,n.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(rb.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,n.jsx)(o.Wu,{children:(0,n.jsxs)("div",{className:"flex gap-4",children:[(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)(c.p,{placeholder:"Cerca per nomenclatura, tipologia o formazione...",value:e,onChange:e=>t(e.target.value),className:"w-full"})}),(0,n.jsx)("div",{className:"flex gap-2",children:["all","installato","in_corso","non_installato"].map(e=>(0,n.jsx)(l.$,{variant:r===e?"default":"outline",size:"sm",onClick:()=>p(e),children:"all"===e?"Tutti":"installato"===e?"Installati":"in_corso"===e?"In Corso":"Da Installare"},e))})]})})]}),(0,n.jsxs)(o.Zp,{children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsxs)(o.ZB,{children:["Elenco Cavi (",K.length,")"]}),(0,n.jsx)(o.BT,{children:"Gestione completa dei cavi con stato installazione, collegamento e certificazione"})]}),(0,n.jsx)(o.Wu,{children:(0,n.jsx)("div",{className:"rounded-md border",children:(0,n.jsxs)(u.XI,{children:[(0,n.jsx)(u.A0,{children:(0,n.jsxs)(u.Hj,{children:[(0,n.jsx)(u.nd,{children:"ID Cavo"}),(0,n.jsx)(u.nd,{children:"Tipologia"}),(0,n.jsx)(u.nd,{children:"Conduttori/Sezione"}),(0,n.jsx)(u.nd,{children:"Partenza → Arrivo"}),(0,n.jsx)(u.nd,{children:"Lunghezza"}),(0,n.jsx)(u.nd,{children:"Bobina"}),(0,n.jsx)(u.nd,{children:"Stato"}),(0,n.jsx)(u.nd,{children:"Collegamento"}),(0,n.jsx)(u.nd,{children:"Certificazione"}),(0,n.jsx)(u.nd,{children:"Azioni"})]})}),(0,n.jsx)(u.BF,{children:x?(0,n.jsx)(u.Hj,{children:(0,n.jsx)(u.nA,{colSpan:10,className:"text-center py-8",children:(0,n.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,n.jsx)(ry.A,{className:"h-4 w-4 animate-spin"}),"Caricamento cavi..."]})})}):v?(0,n.jsx)(u.Hj,{children:(0,n.jsx)(u.nA,{colSpan:10,className:"text-center py-8",children:(0,n.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,n.jsx)(rv.A,{className:"h-4 w-4"}),v]})})}):0===K.length?(0,n.jsx)(u.Hj,{children:(0,n.jsx)(u.nA,{colSpan:10,className:"text-center py-8 text-slate-500",children:"Nessun cavo trovato"})}):K.map(e=>(0,n.jsxs)(u.Hj,{children:[(0,n.jsx)(u.nA,{className:"font-medium",children:e.id_cavo}),(0,n.jsx)(u.nA,{children:e.tipologia||"-"}),(0,n.jsx)(u.nA,{children:(0,n.jsxs)("div",{className:"text-sm",children:[(0,n.jsx)("div",{children:e.n_conduttori||"-"}),(0,n.jsx)("div",{className:"text-slate-500",children:e.sezione||"-"})]})}),(0,n.jsx)(u.nA,{children:(0,n.jsxs)("div",{className:"text-sm",children:[(0,n.jsx)("div",{className:"font-medium",children:e.ubicazione_partenza||"-"}),(0,n.jsx)("div",{className:"text-slate-500",children:"↓"}),(0,n.jsx)("div",{className:"font-medium",children:e.ubicazione_arrivo||"-"})]})}),(0,n.jsx)(u.nA,{children:(0,n.jsxs)("div",{className:"text-sm",children:[(0,n.jsxs)("div",{children:[e.metratura_reale||0,"/",e.metri_teorici||0,"m"]}),(0,n.jsxs)("div",{className:"text-slate-500",children:[e.metri_teorici?Math.round((e.metratura_reale||0)/e.metri_teorici*100):0,"%"]})]})}),(0,n.jsx)(u.nA,{children:(0,n.jsx)(s.E,{variant:"BOBINA_VUOTA"===e.id_bobina?"destructive":"secondary",children:e.id_bobina||"BOBINA_VUOTA"})}),(0,n.jsx)(u.nA,{children:G(e.stato_installazione)}),(0,n.jsx)(u.nA,{children:$(e.collegamenti)}),(0,n.jsx)(u.nA,{children:U(e)}),(0,n.jsx)(u.nA,{children:(0,n.jsxs)("div",{className:"flex gap-1",children:[(0,n.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>W(e),disabled:"installato"===e.stato_installazione,children:(0,n.jsx)(rw.A,{className:"h-4 w-4"})}),(0,n.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>F(e),children:(0,n.jsx)(rj.A,{className:"h-4 w-4"})}),(0,n.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>q(e),children:(0,n.jsx)(rN.A,{className:"h-4 w-4"})})]})})]},e.id_cavo))})]})})})]}),(0,n.jsx)(f.lG,{open:A,onOpenChange:S,children:(0,n.jsxs)(f.Cf,{children:[(0,n.jsxs)(f.c7,{children:[(0,n.jsx)(f.L3,{children:"Inserisci Metri Posati"}),(0,n.jsxs)(f.rr,{children:["Cavo: ",y?.id_cavo]})]}),(0,n.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(d.J,{htmlFor:"metri_posati",children:"Metri Posati"}),(0,n.jsx)(c.p,{id:"metri_posati",type:"number",value:T.metri_posati,onChange:e=>P({...T,metri_posati:parseInt(e.target.value)||0})})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(d.J,{htmlFor:"bobina_metri",children:"Bobina"}),(0,n.jsxs)(rn,{value:T.id_bobina,onValueChange:e=>P({...T,id_bobina:e}),children:[(0,n.jsx)(ra,{children:(0,n.jsx)(ri,{placeholder:"Seleziona bobina"})}),(0,n.jsxs)(ro,{children:[(0,n.jsx)(rl,{value:"BOBINA_VUOTA",children:"BOBINA_VUOTA"}),z.map(e=>(0,n.jsxs)(rl,{value:e.id_bobina,children:[e.numero_bobina," - ",e.tipologia," (",e.metri_residui,"m)"]},e.id_bobina))]})]})]})]}),(0,n.jsxs)(f.Es,{children:[(0,n.jsx)(l.$,{variant:"outline",onClick:()=>S(!1),children:"Annulla"}),(0,n.jsx)(l.$,{onClick:D,children:"Salva"})]})]})}),(0,n.jsx)(f.lG,{open:k,onOpenChange:R,children:(0,n.jsxs)(f.Cf,{children:[(0,n.jsxs)(f.c7,{children:[(0,n.jsx)(f.L3,{children:"Modifica Bobina"}),(0,n.jsxs)(f.rr,{children:["Cavo: ",y?.id_cavo]})]}),(0,n.jsx)("div",{className:"grid gap-4 py-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(d.J,{htmlFor:"bobina_modifica",children:"Nuova Bobina"}),(0,n.jsxs)(rn,{value:T.id_bobina,onValueChange:e=>P({...T,id_bobina:e}),children:[(0,n.jsx)(ra,{children:(0,n.jsx)(ri,{placeholder:"Seleziona bobina"})}),(0,n.jsxs)(ro,{children:[(0,n.jsx)(rl,{value:"BOBINA_VUOTA",children:"BOBINA_VUOTA"}),z.map(e=>(0,n.jsxs)(rl,{value:e.id_bobina,children:[e.numero_bobina," - ",e.tipologia," (",e.metri_residui,"m)"]},e.id_bobina))]})]})]})}),(0,n.jsxs)(f.Es,{children:[(0,n.jsx)(l.$,{variant:"outline",onClick:()=>R(!1),children:"Annulla"}),(0,n.jsx)(l.$,{onClick:H,children:"Salva"})]})]})})]})})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var n=r(60687);r(43210);var i=r(78148),a=r(4780);function o({className:e,...t}){return(0,n.jsx)(i.b,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(60687);r(43210);var i=r(4780);function a({className:e,type:t,...r}){return(0,n.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,341,658,24,692],()=>r(52270));module.exports=n})();