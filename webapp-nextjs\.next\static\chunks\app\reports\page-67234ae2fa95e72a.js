(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[22],{24944:(e,t,a)=>{"use strict";a.d(t,{k:()=>n});var s=a(95155);a(12115);var i=a(55863),r=a(59434);function n(e){let{className:t,value:a,...n}=e;return(0,s.jsx)(i.bL,{"data-slot":"progress",className:(0,r.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...n,children:(0,s.jsx)(i.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})}},25731:(e,t,a)=>{"use strict";a.d(t,{AR:()=>l,At:()=>n,CV:()=>o,FH:()=>i,Fw:()=>c,ZQ:()=>r,_I:()=>m,ug:()=>d});let s=a(23464).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"),Promise.reject(e)});let i={get:async(e,t)=>(await s.get(e,t)).data,post:async(e,t,a)=>(await s.post(e,t,a)).data,put:async(e,t,a)=>(await s.put(e,t,a)).data,patch:async(e,t,a)=>(await s.patch(e,t,a)).data,delete:async(e,t)=>(await s.delete(e,t)).data},r={login:async e=>{let t=new FormData;return t.append("username",e.username),t.append("password",e.password),(await s.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>i.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>i.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},n={getCavi:(e,t)=>i.get("/api/cavi/".concat(e),{params:t}),getCavo:(e,t)=>i.get("/api/cavi/".concat(e,"/").concat(t)),createCavo:(e,t)=>i.post("/api/cavi/".concat(e),t),updateCavo:(e,t,a)=>i.put("/api/cavi/".concat(e,"/").concat(t),a),deleteCavo:(e,t)=>i.delete("/api/cavi/".concat(e,"/").concat(t)),updateMetriPosati:(e,t,a)=>i.patch("/api/cavi/".concat(e,"/").concat(t,"/metri-posati"),{metri_posati:a}),updateBobina:(e,t,a)=>i.patch("/api/cavi/".concat(e,"/").concat(t,"/bobina"),{id_bobina:a}),updateCollegamento:(e,t,a)=>i.patch("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),{collegamenti:a})},c={getBobine:e=>i.get("/api/parco-cavi/".concat(e)),getBobina:(e,t)=>i.get("/api/parco-cavi/".concat(e,"/").concat(t)),createBobina:(e,t)=>i.post("/api/parco-cavi/".concat(e),t),updateBobina:(e,t,a)=>i.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>i.delete("/api/parco-cavi/".concat(e,"/").concat(t))},o={getComande:e=>i.get("/api/comande/".concat(e)),getComanda:(e,t)=>i.get("/api/comande/".concat(e,"/").concat(t)),createComanda:(e,t)=>i.post("/api/comande/".concat(e),t),updateComanda:(e,t,a)=>i.put("/api/comande/".concat(e,"/").concat(t),a),deleteComanda:(e,t)=>i.delete("/api/comande/".concat(e,"/").concat(t)),assegnaCavi:(e,t,a)=>i.post("/api/comande/".concat(e,"/").concat(t,"/assegna-cavi"),{cavi_ids:a})},l={getResponsabili:e=>i.get("/api/responsabili/".concat(e)),createResponsabile:(e,t)=>i.post("/api/responsabili/".concat(e),t),updateResponsabile:(e,t,a)=>i.put("/api/responsabili/".concat(e,"/").concat(t),a),deleteResponsabile:(e,t)=>i.delete("/api/responsabili/".concat(e,"/").concat(t))},d={getReportAvanzamento:e=>i.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>i.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>i.get("/api/reports/".concat(e,"/utilizzo-bobine")),getReportProgress:e=>i.get("/api/reports/".concat(e,"/progress"))},m={getCantieri:()=>i.get("/api/cantieri"),getCantiere:e=>i.get("/api/cantieri/".concat(e)),createCantiere:e=>i.post("/api/cantieri",e),updateCantiere:(e,t)=>i.put("/api/cantieri/".concat(e),t)}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var s=a(95155);a(12115);var i=a(99708),r=a(74466),n=a(59434);let c=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:r,asChild:o=!1,...l}=e,d=o?i.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,n.cn)(c({variant:a,size:r,className:t})),...l})}},40283:(e,t,a)=>{"use strict";a.d(t,{A:()=>c,AuthProvider:()=>o});var s=a(95155),i=a(12115),r=a(25731);let n=(0,i.createContext)(void 0);function c(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function o(e){let{children:t}=e,[a,c]=(0,i.useState)(null),[o,l]=(0,i.useState)(null),[d,m]=(0,i.useState)(!0);(0,i.useEffect)(()=>{u()},[]);let u=async()=>{try{if(!localStorage.getItem("access_token"))return void m(!1);let e=await r.ZQ.verifyToken();if("cantieri_user"===e.role){let t={id_cantiere:e.cantiere_id||0,commessa:e.cantiere_name||e.username,codice_univoco:"",id_utente:e.user_id};l(t),c(null)}else{let t={id_utente:e.user_id,username:e.username,ruolo:e.role};c(t),l(null)}}catch(e){console.error("Errore verifica autenticazione:",e),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data")}finally{m(!1)}},p=async(e,t)=>{try{m(!0);let a=await r.ZQ.login({username:e,password:t});localStorage.setItem("access_token",a.access_token),document.cookie="access_token=".concat(a.access_token,"; path=/; max-age=").concat(86400);let s={id_utente:a.user_id,username:a.username,ruolo:a.role};localStorage.setItem("user_data",JSON.stringify(s)),c(s),l(null)}catch(e){throw console.error("Errore login:",e),e}finally{m(!1)}},x=async(e,t)=>{try{m(!0);let a=await r.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});localStorage.setItem("access_token",a.access_token),document.cookie="access_token=".concat(a.access_token,"; path=/; max-age=").concat(86400);let s={id_cantiere:a.cantiere_id,commessa:a.cantiere_name,codice_univoco:e,id_utente:a.user_id};localStorage.setItem("cantiere_data",JSON.stringify(s)),l(s),c(null)}catch(e){throw console.error("Errore login cantiere:",e),e}finally{m(!1)}};return(0,s.jsx)(n.Provider,{value:{user:a,cantiere:o,isAuthenticated:!!a||!!o,isLoading:d,login:p,loginCantiere:x,logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),document.cookie="access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",c(null),l(null),window.location.href="/login"},checkAuth:u},children:t})}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var s=a(52596),i=a(39688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,i.QP)((0,s.$)(t))}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>c,Zp:()=>r,aR:()=>n});var s=a(95155);a(12115);var i=a(59434);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...a})}},79680:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>B});var s=a(95155),i=a(12115),r=a(66695),n=a(30285),c=a(24944),o=a(40283),l=a(25731),d=a(72713),m=a(91788),u=a(51154),p=a(85339),x=a(16785),h=a(33109),g=a(79397),v=a(40646),f=a(14186),j=a(69074),b=a(83540),_=a(3401),w=a(94754),N=a(96025),y=a(16238),k=a(94517),C=a(83394),S=a(8782),A=a(34e3),z=a(54811);function B(){let[e,t]=(0,i.useState)("month"),[a,B]=(0,i.useState)(null),[R,Z]=(0,i.useState)(null),[I,T]=(0,i.useState)(!0),[P,E]=(0,i.useState)(""),{user:F,cantiere:W}=(0,o.A)();(0,i.useEffect)(()=>{D()},[]);let D=async()=>{try{T(!0),E("");let e=(null==W?void 0:W.id_cantiere)||(null==F?void 0:F.id_utente);if(!e)return void E("Cantiere non selezionato");let[t,a]=await Promise.all([l.ug.getReportAvanzamento(e),l.ug.getReportBOQ(e)]);B(t),Z(a)}catch(a){var e,t;console.error("Errore caricamento report:",a),E((null==(t=a.response)||null==(e=t.data)?void 0:e.detail)||"Errore durante il caricamento dei report")}finally{T(!1)}},Q=a?[{name:"Installati",value:a.cavi_posati,color:"#22c55e"},{name:"Collegati",value:a.cavi_collegati,color:"#3b82f6"},{name:"Certificati",value:a.cavi_certificati,color:"#f59e0b"},{name:"Da Installare",value:a.totale_cavi-a.cavi_posati,color:"#94a3b8"}]:[];return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 text-blue-600"}),"Report e Analytics"]}),(0,s.jsx)("p",{className:"text-slate-600 mt-1",children:"Analisi dettagliate dell'avanzamento del cantiere"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[["week","month","quarter"].map(a=>(0,s.jsx)(n.$,{variant:e===a?"default":"outline",size:"sm",onClick:()=>t(a),className:"capitalize",children:"week"===a?"Settimana":"month"===a?"Mese":"Trimestre"},a)),(0,s.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Esporta PDF"]})]})]}),I?(0,s.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(u.A,{className:"h-6 w-6 animate-spin"}),(0,s.jsx)("span",{children:"Caricamento report..."})]})}):P?(0,s.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 text-red-600",children:[(0,s.jsx)(p.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{children:P})]})}):a?(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)(r.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento Totale"}),(0,s.jsx)(x.A,{className:"h-4 w-4 text-blue-500"})]}),(0,s.jsxs)(r.Wu,{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[a.percentuale_completamento.toFixed(1),"%"]}),(0,s.jsx)(c.k,{value:a.percentuale_completamento,className:"mt-2"}),(0,s.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[a.cavi_posati," di ",a.totale_cavi," cavi"]})]})]}),(0,s.jsxs)(r.Zp,{className:"border-l-4 border-l-green-500",children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Media Giornaliera"}),(0,s.jsx)(h.A,{className:"h-4 w-4 text-green-500"})]}),(0,s.jsxs)(r.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:a.media_giornaliera.toFixed(1)}),(0,s.jsx)("p",{className:"text-xs text-slate-500",children:"cavi/giorno"}),(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsx)(h.A,{className:"h-3 w-3 text-green-500 mr-1"}),(0,s.jsxs)("span",{className:"text-xs text-green-600",children:["Basato su ",a.giorni_lavorativi," giorni"]})]})]})]}),(0,s.jsxs)(r.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Completati"}),(0,s.jsx)(g.A,{className:"h-4 w-4 text-purple-500"})]}),(0,s.jsxs)(r.Wu,{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[a.percentuale_metri.toFixed(1),"%"]}),(0,s.jsxs)("p",{className:"text-xs text-slate-500",children:[a.metri_posati,"m di ",a.metri_totali,"m"]}),(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsx)(v.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,s.jsx)("span",{className:"text-xs text-purple-600",children:"Metrature"})]})]})]}),(0,s.jsxs)(r.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Stima Completamento"}),(0,s.jsx)(f.A,{className:"h-4 w-4 text-orange-500"})]}),(0,s.jsxs)(r.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:a.stima_completamento}),(0,s.jsx)("p",{className:"text-xs text-slate-500",children:"al ritmo attuale"}),(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsx)(j.A,{className:"h-3 w-3 text-orange-500 mr-1"}),(0,s.jsx)("span",{className:"text-xs text-orange-600",children:"Proiezione"})]})]})]})]}):null,(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{children:[(0,s.jsx)(r.ZB,{children:"Trend Installazioni Settimanali"}),(0,s.jsx)(r.BT,{children:"Confronto installazioni vs target giornaliero"})]}),(0,s.jsx)(r.Wu,{children:(0,s.jsx)(b.u,{width:"100%",height:300,children:(0,s.jsxs)(_.E,{data:[],children:[(0,s.jsx)(w.d,{strokeDasharray:"3 3"}),(0,s.jsx)(N.W,{dataKey:"name"}),(0,s.jsx)(y.h,{}),(0,s.jsx)(k.m,{}),(0,s.jsx)(C.y,{dataKey:"target",fill:"#e2e8f0",name:"Target"}),(0,s.jsx)(C.y,{dataKey:"installati",fill:"#3b82f6",name:"Installati"})]})})})]}),(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{children:[(0,s.jsx)(r.ZB,{children:"Distribuzione Stato Cavi"}),(0,s.jsx)(r.BT,{children:"Panoramica dello stato di avanzamento"})]}),(0,s.jsx)(r.Wu,{children:(0,s.jsx)(b.u,{width:"100%",height:300,children:(0,s.jsxs)(S.r,{children:[(0,s.jsx)(A.F,{data:Q,cx:"50%",cy:"50%",outerRadius:100,fill:"#8884d8",dataKey:"value",label:e=>{let{name:t,percent:a}=e;return"".concat(t," ").concat((100*a).toFixed(0),"%")},children:Q.map((e,t)=>(0,s.jsx)(z.f,{fill:e.color},"cell-".concat(t)))}),(0,s.jsx)(k.m,{})]})})})]})]}),(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{children:[(0,s.jsx)(r.ZB,{children:"Avanzamento per Settore"}),(0,s.jsx)(r.BT,{children:"Stato di completamento dettagliato per ogni settore"})]}),(0,s.jsx)(r.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Dati di avanzamento per settore saranno disponibili quando collegati al backend"})})})]}),(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{children:[(0,s.jsx)(r.ZB,{children:"Performance Team"}),(0,s.jsx)(r.BT,{children:"Statistiche dettagliate per ogni squadra di lavoro"})]}),(0,s.jsx)(r.Wu,{children:(0,s.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Statistiche team saranno disponibili quando collegati al backend"})})]})]})})}},89569:(e,t,a)=>{Promise.resolve().then(a.bind(a,79680))}},e=>{var t=t=>e(e.s=t);e.O(0,[836,83,763,441,684,358],()=>t(89569)),_N_E=e.O()}]);