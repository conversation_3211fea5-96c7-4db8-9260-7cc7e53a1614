'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { User, Cantier<PERSON> } from '@/types'
import { authApi } from '@/lib/api'

interface AuthContextType {
  user: User | null
  cantiere: Cantiere | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (username: string, password: string) => Promise<void>
  loginCantiere: (codice_cantiere: string, password_cantiere: string) => Promise<void>
  logout: () => void
  checkAuth: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [cantiere, setCantiere] = useState<Cantiere | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user || !!cantiere

  // Verifica l'autenticazione al caricamento
  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        setIsLoading(false)
        return
      }

      const response = await authApi.verifyToken()

      // Il backend restituisce i dati dell'utente direttamente
      if (response.role === 'cantieri_user') {
        // È un login cantiere - ricostruisci i dati del cantiere
        const cantiereData = {
          id_cantiere: response.cantiere_id || 0,
          commessa: response.cantiere_name || response.username,
          codice_univoco: '',
          id_utente: response.user_id
        }
        setCantiere(cantiereData)
        setUser(null)
      } else {
        // È un login utente normale
        const userData = {
          id_utente: response.user_id,
          username: response.username,
          ruolo: response.role
        }
        setUser(userData)
        setCantiere(null)
      }
    } catch (error) {
      console.error('Errore verifica autenticazione:', error)
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_data')
      localStorage.removeItem('cantiere_data')
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (username: string, password: string) => {
    try {
      setIsLoading(true)
      const response = await authApi.login({ username, password })

      localStorage.setItem('access_token', response.access_token)

      // Salva il token anche nei cookie per il middleware
      document.cookie = `access_token=${response.access_token}; path=/; max-age=${24 * 60 * 60}` // 24 ore

      // Il backend restituisce i dati dell'utente direttamente nella risposta
      const userData = {
        id_utente: response.user_id,
        username: response.username,
        ruolo: response.role
      }

      localStorage.setItem('user_data', JSON.stringify(userData))
      setUser(userData)
      setCantiere(null)
    } catch (error) {
      console.error('Errore login:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const loginCantiere = async (codice_cantiere: string, password_cantiere: string) => {
    try {
      setIsLoading(true)
      const response = await authApi.loginCantiere({ codice_cantiere, password_cantiere })

      localStorage.setItem('access_token', response.access_token)

      // Salva il token anche nei cookie per il middleware
      document.cookie = `access_token=${response.access_token}; path=/; max-age=${24 * 60 * 60}` // 24 ore

      // Il backend restituisce i dati del cantiere direttamente nella risposta
      const cantiereData = {
        id_cantiere: response.cantiere_id,
        commessa: response.cantiere_name,
        codice_univoco: codice_cantiere,
        id_utente: response.user_id
      }

      localStorage.setItem('cantiere_data', JSON.stringify(cantiereData))
      setCantiere(cantiereData)
      setUser(null)
    } catch (error) {
      console.error('Errore login cantiere:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    localStorage.removeItem('access_token')
    localStorage.removeItem('user_data')
    localStorage.removeItem('cantiere_data')

    // Rimuovi il token anche dai cookie
    document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'

    setUser(null)
    setCantiere(null)
    window.location.href = '/login'
  }

  const value: AuthContextType = {
    user,
    cantiere,
    isAuthenticated,
    isLoading,
    login,
    loginCantiere,
    logout,
    checkAuth,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
