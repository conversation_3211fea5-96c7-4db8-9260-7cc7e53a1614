import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const token = request.cookies.get('access_token')?.value ||
                request.headers.get('authorization')?.replace('Bearer ', '')

  const isLoginPage = request.nextUrl.pathname === '/login'

  // Se è la pagina di login e l'utente è già autenticato, reindirizza alla dashboard
  if (isLoginPage && token) {
    return NextResponse.redirect(new URL('/', request.url))
  }

  // Se non è autenticato e non è nella pagina di login, reindirizza al login
  if (!token && !isLoginPage) {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
