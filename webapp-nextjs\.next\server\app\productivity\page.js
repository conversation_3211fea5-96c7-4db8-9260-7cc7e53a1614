(()=>{var e={};e.id=451,e.ids=[451],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5900:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>o});var a=s(65239),r=s(48088),i=s(88170),l=s.n(i),n=s(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let o={children:["",{children:["productivity",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,30535)),"C:\\CMS\\webapp-nextjs\\src\\app\\productivity\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\productivity\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/productivity/page",pathname:"/productivity",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11273:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,q:()=>i});var a=s(43210),r=s(60687);function i(e,t){let s=a.createContext(t),i=e=>{let{children:t,...i}=e,l=a.useMemo(()=>i,Object.values(i));return(0,r.jsx)(s.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(r){let i=a.useContext(s);if(i)return i;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}function l(e,t=[]){let s=[],i=()=>{let t=s.map(e=>a.createContext(e));return function(s){let r=s?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...s,[e]:r}}),[s,r])}};return i.scopeName=e,[function(t,i){let l=a.createContext(i),n=s.length;s=[...s,i];let c=t=>{let{scope:s,children:i,...c}=t,o=s?.[e]?.[n]||l,d=a.useMemo(()=>c,Object.values(c));return(0,r.jsx)(o.Provider,{value:d,children:i})};return c.displayName=t+"Provider",[c,function(s,r){let c=r?.[e]?.[n]||l,o=a.useContext(c);if(o)return o;if(void 0!==i)return i;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=s.reduce((t,{useScope:s,scopeName:a})=>{let r=s(e)[`__scope${a}`];return{...t,...r}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return s.scopeName=t.scopeName,s}(i,...t)]}},12412:e=>{"use strict";e.exports=require("assert")},14163:(e,t,s)=>{"use strict";s.d(t,{hO:()=>c,sG:()=>n});var a=s(43210),r=s(51215),i=s(8730),l=s(60687),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,i.TL)(`Primitive.${t}`),r=a.forwardRef((e,a)=>{let{asChild:r,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(r?s:t,{...i,ref:a})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function c(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21020:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(60687),r=s(43210),i=s(44493),l=s(29523),n=s(96834),c=s(46657),o=s(58559),d=s(28947);let m=(0,s(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);var x=s(25541),u=s(41312),p=s(40228),f=s(48730),h=s(53411);function v(){let[e,t]=(0,r.useState)("week");return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 text-blue-600"}),"Dashboard Produttivit\xe0"]}),(0,a.jsx)("p",{className:"text-slate-600 mt-1",children:"Monitoraggio avanzamento cantiere in tempo reale"})]}),(0,a.jsx)("div",{className:"flex gap-2",children:["day","week","month"].map(s=>(0,a.jsx)(l.$,{variant:e===s?"default":"outline",size:"sm",onClick:()=>t(s),className:"capitalize",children:"day"===s?"Oggi":"week"===s?"Settimana":"Mese"},s))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Avanzamento Totale"}),(0,a.jsx)(d.A,{className:"h-4 w-4 text-blue-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[71.2,"%"]}),(0,a.jsx)(c.k,{value:71.2,className:"mt-2"}),(0,a.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[890," di ",1250," cavi"]})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-green-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Velocit\xe0 Installazione"}),(0,a.jsx)(m,{className:"h-4 w-4 text-green-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:12.5}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"cavi/ora per persona"}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(x.A,{className:"h-3 w-3 text-green-500 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-green-600",children:"+12% vs settimana scorsa"})]})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Team Attivi"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-orange-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:8}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"squadre operative"}),(0,a.jsx)("div",{className:"flex items-center mt-2",children:(0,a.jsx)(n.E,{variant:"secondary",className:"text-xs",children:"16 persone"})})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento Stimato"}),(0,a.jsx)(p.A,{className:"h-4 w-4 text-purple-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:"15 giorni"}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"al ritmo attuale"}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(f.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-purple-600",children:"Aggiornato ora"})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-blue-600"}),"Performance Team"]}),(0,a.jsx)(i.BT,{children:"Statistiche dettagliate per squadra"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{name:"Team Alpha",members:4,installed:156,connected:98,certified:67,efficiency:92},{name:"Team Beta",members:3,installed:134,connected:89,certified:54,efficiency:88},{name:"Team Gamma",members:5,installed:189,connected:145,certified:89,efficiency:95},{name:"Team Delta",members:4,installed:167,connected:123,certified:78,efficiency:90}].map((e,t)=>(0,a.jsx)("div",{className:"flex items-center justify-between p-3 bg-slate-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-slate-900",children:e.name}),(0,a.jsxs)(n.E,{variant:e.efficiency>=90?"default":"secondary",children:[e.efficiency,"% efficienza"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm text-slate-600",children:[(0,a.jsxs)("div",{children:["Installati: ",(0,a.jsx)("span",{className:"font-medium text-slate-900",children:e.installed})]}),(0,a.jsxs)("div",{children:["Collegati: ",(0,a.jsx)("span",{className:"font-medium text-slate-900",children:e.connected})]}),(0,a.jsxs)("div",{children:["Certificati: ",(0,a.jsx)("span",{className:"font-medium text-slate-900",children:e.certified})]})]}),(0,a.jsx)(c.k,{value:e.efficiency,className:"mt-2 h-2"})]})},t))})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-green-600"}),"Attivit\xe0 Recenti"]}),(0,a.jsx)(i.BT,{children:"Ultime operazioni completate"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{time:"10:30",action:"Installazione completata",details:"Cavo FG16OM4-24 - Settore A",team:"Alpha"},{time:"10:15",action:"Collegamento certificato",details:"Cavo MM-OM3-12 - Settore B",team:"Beta"},{time:"09:45",action:"Nuova installazione",details:"Cavo SM-G652D-48 - Settore C",team:"Gamma"},{time:"09:30",action:"Test completato",details:"Cavo FG16OM4-12 - Settore A",team:"Delta"}].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-slate-50 rounded-lg",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-900",children:e.action}),(0,a.jsx)("span",{className:"text-xs text-slate-500",children:e.time})]}),(0,a.jsx)("p",{className:"text-sm text-slate-600 truncate",children:e.details}),(0,a.jsx)(n.E,{variant:"outline",className:"mt-1 text-xs",children:e.team})]})]},t))})})]})]})]})})}},21820:e=>{"use strict";e.exports=require("os")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28947:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30535:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\productivity\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\productivity\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},37776:(e,t,s)=>{Promise.resolve().then(s.bind(s,30535))},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>n,Zp:()=>i,aR:()=>l});var a=s(60687);s(43210);var r=s(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}},46657:(e,t,s)=>{"use strict";s.d(t,{k:()=>b});var a=s(60687),r=s(43210),i=s(11273),l=s(14163),n="Progress",[c,o]=(0,i.A)(n),[d,m]=c(n),x=r.forwardRef((e,t)=>{var s,r;let{__scopeProgress:i,value:n=null,max:c,getValueLabel:o=f,...m}=e;(c||0===c)&&!j(c)&&console.error((s=`${c}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let x=j(c)?c:100;null===n||g(n,x)||console.error((r=`${n}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=g(n,x)?n:null,p=v(u)?o(u,x):void 0;return(0,a.jsx)(d,{scope:i,value:u,max:x,children:(0,a.jsx)(l.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":v(u)?u:void 0,"aria-valuetext":p,role:"progressbar","data-state":h(u,x),"data-value":u??void 0,"data-max":x,...m,ref:t})})});x.displayName=n;var u="ProgressIndicator",p=r.forwardRef((e,t)=>{let{__scopeProgress:s,...r}=e,i=m(u,s);return(0,a.jsx)(l.sG.div,{"data-state":h(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...r,ref:t})});function f(e,t){return`${Math.round(e/t*100)}%`}function h(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function v(e){return"number"==typeof e}function j(e){return v(e)&&!isNaN(e)&&e>0}function g(e,t){return v(e)&&!isNaN(e)&&e<=t&&e>=0}p.displayName=u;var N=s(4780);function b({className:e,value:t,...s}){return(0,a.jsx)(x,{"data-slot":"progress",className:(0,N.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,a.jsx)(p,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}},47504:(e,t,s)=>{Promise.resolve().then(s.bind(s,21020))},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,341,658,692],()=>s(5900));module.exports=a})();